# Ko-fi Webhook Setup

This document explains how to set up Ko-fi webhooks for InterChat donation processing.

## Overview

Ko-fi webhooks have been moved from the Discord bot API to the dashboard because:
- Ko-fi requires HTTPS URLs for webhooks
- The dashboard runs on Vercel with automatic HTTPS
- The Discord bot runs on a VPS without HTTPS by default

## Webhook Endpoint

**URL**: `https://your-dashboard-domain.vercel.app/api/webhooks/kofi`

## Environment Variables

Add the following environment variable to your dashboard `.env` file:

```env
KOFI_VERIFICATION_TOKEN=your-kofi-verification-token-here
```

You can get this token from your Ko-fi webhook settings page.

## Ko-fi Configuration

1. Go to your Ko-fi account settings
2. Navigate to "Webhooks" section
3. Set the webhook URL to: `https://your-dashboard-domain.vercel.app/api/webhooks/kofi`
4. Copy the verification token and add it to your environment variables

**Important**: Ko-fi sends webhook data as `application/x-www-form-urlencoded` with a `data` field containing the payment information as a JSON string. Our endpoint validates this content type and properly parses the form data.

## Supported Ko-fi Events

The webhook handles the following Ko-fi payment types:
- **Single Donations**: One-time donations
- **Subscriptions**: Monthly recurring payments
- **Membership Tiers**: Specific tier subscriptions (e.g., "Supporter" tier)

## Premium Features

### Ko-fi Supporter Tier ($3/month)
When a user subscribes to the "Supporter" tier:
- Grants unlimited media sharing in InterChat calls
- Updates user's `hasMediaPremium` status in the database
- Provides premium hub name customization features

## Database Schema

The webhook stores donation data in the `Donation` model:

```prisma
model Donation {
  id                String    @id @default(cuid())
  kofiTransactionId String    @unique
  messageId         String    @unique
  amount            Float
  currency          String
  fromName          String
  message           String?
  email             String?
  isPublic          Boolean   @default(true)
  kofiTimestamp     DateTime
  kofiUrl           String?
  isSubscription    Boolean   @default(false)
  isFirstPayment    Boolean   @default(false)
  tierName          String?
  discordUserId     String?   // Linked Discord user (if available)
  processed         Boolean   @default(false)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}
```

## User Linking

✅ **IMPLEMENTED**: Users can now link their Ko-fi account to their Discord account!

### How It Works
1. **User Links Account**: Users enter their Ko-fi email in the dashboard (`/api/user/link-kofi`)
2. **Automatic Processing**: When Ko-fi webhooks arrive, the system automatically:
   - Finds the linked Discord user by email
   - Grants premium benefits for Ko-fi Supporter tier
   - Marks donations as processed
   - Logs premium access notifications

### API Endpoints
- `POST /api/user/link-kofi` - Link Ko-fi account by email
- `GET /api/user/link-kofi` - Get current linking status and recent donations
- `DELETE /api/user/link-kofi` - Unlink Ko-fi account
- `POST /api/admin/process-unlinked-donations` - Admin tool to process historical donations

### Retroactive Processing
The system can process historical donations:
- When users link their account, existing unprocessed donations are automatically processed
- Admin endpoint available to batch process unlinked donations
- Premium benefits are granted based on donation date (30 days from donation or current date)

## Frontend Integration

### Ko-fi Linking Component
A React component is available for the dashboard: `src/components/KofiLinking.tsx`

Features:
- Link/unlink Ko-fi account by email
- Display current premium status
- Show recent Ko-fi donations
- Automatic processing of historical donations
- Real-time status updates

Usage:
```tsx
import { KofiLinking } from "@/components/KofiLinking";

export default function SettingsPage() {
  return (
    <div>
      <h1>Account Settings</h1>
      <KofiLinking />
    </div>
  );
}
```

## Testing

Use the provided test script to verify webhook functionality:

```bash
# 1. Start the dashboard development server
npm run dev

# 2. Set the verification token in .env
KOFI_VERIFICATION_TOKEN=05ede22e-9a69-45f9-a234-402fda2afca2

# 3. Test the Ko-fi linking functionality in the dashboard
# 4. Make a test Ko-fi donation to verify automatic processing
```

## Payload Examples

The webhook handles real Ko-fi payload formats:

### Subscription Payment
```json
{
  "verification_token": "uuid",
  "message_id": "uuid",
  "timestamp": "2025-06-24T06:47:43Z",
  "type": "Subscription",
  "is_public": true,
  "from_name": "Jo Example",
  "message": null,
  "amount": "5.00",
  "currency": "USD",
  "is_subscription_payment": true,
  "is_first_subscription_payment": false,
  "kofi_transaction_id": "uuid",
  "tier_name": "Bronze"
}
```

### Single Donation
```json
{
  "verification_token": "uuid",
  "message_id": "uuid",
  "timestamp": "2025-06-24T06:47:43Z",
  "type": "Donation",
  "is_public": true,
  "from_name": "Jo Example",
  "message": "Good luck with the integration!",
  "amount": "3.00",
  "currency": "USD",
  "is_subscription_payment": false,
  "is_first_subscription_payment": false,
  "kofi_transaction_id": "uuid",
  "tier_name": null
}
```

## Security

- All webhooks are validated using Ko-fi's verification token
- Duplicate transactions are automatically detected and rejected
- Payload structure is validated using Zod schemas
- All errors are logged for debugging

## Monitoring

The webhook endpoint logs:
- Successful donation processing
- Validation errors
- Database operations
- Premium benefit granting

Check the Vercel function logs for webhook activity and debugging information.
