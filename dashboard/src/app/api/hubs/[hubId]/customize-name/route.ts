import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

type Props = {
  params: Promise<{ hubId: string }>;
};

// Schema for customizing hub name
const customizeNameSchema = z.object({
  name: z
    .string()
    .min(3, "Hub name must be at least 3 characters")
    .max(50, "Hub name cannot exceed 50 characters")
    .regex(
      /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/,
      "Hub name contains invalid characters"
    )
    .refine(
      (name) => /[a-zA-Z0-9]/.test(name),
      "Hub name must contain at least one letter or number"
    )
    .transform((name) => name.trim().replace(/\s+/g, ' ')), // Sanitize whitespace
});

/**
 * Check if user has Ko-fi Supporter tier (premium status)
 * This is a simplified check - in production, this would integrate with the actual DonationManager
 */
async function checkPremiumStatus(userId: string): Promise<boolean> {
  try {
    // Check if user has active media premium (Ko-fi Supporter tier grants this)
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        hasMediaPremium: true,
        mediaPremiumExpiresAt: true,
      },
    });

    if (!user?.hasMediaPremium) return false;

    // If no expiration date, it's permanent premium
    if (!user.mediaPremiumExpiresAt) return true;

    // Check if premium hasn't expired
    return user.mediaPremiumExpiresAt > new Date();
  } catch (error) {
    console.error("Error checking premium status:", error);
    return false;
  }
}

async function handlePATCH(request: NextRequest, { params }: Props) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await params;

    // Validate hubId format
    if (!hubId || typeof hubId !== "string") {
      return NextResponse.json(
        { error: "Invalid hub ID" },
        { status: 400 }
      );
    }

    // Check if hub exists and user is the owner
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        name: true,
        ownerId: true,
      },
    });

    if (!hub) {
      return NextResponse.json(
        { error: "Hub not found" },
        { status: 404 }
      );
    }

    // Check if user is the hub owner
    if (hub.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: "Only hub owners can change the hub name" },
        { status: 403 }
      );
    }

    // Check premium status
    const hasPremium = await checkPremiumStatus(session.user.id);
    if (!hasPremium) {
      return NextResponse.json(
        {
          error: "Premium feature required",
          message: "Hub name customization is available to Ko-fi Supporters ($2.99/month)",
          upgradeUrl: "https://ko-fi.com/interchat",
        },
        { status: 402 } // Payment Required
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = customizeNameSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid input",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { name: newName } = validation.data;

    // Check if a hub with this name already exists
    const existingHub = await prisma.hub.findUnique({
      where: { name: newName },
      select: { id: true },
    });

    if (existingHub && existingHub.id !== hubId) {
      return NextResponse.json(
        {
          error: "Name already taken",
          message: `A hub with the name "${newName}" already exists. Please choose a different name.`,
        },
        { status: 409 } // Conflict
      );
    }

    // Update the hub with the new name
    const updatedHub = await prisma.hub.update({
      where: { id: hubId },
      data: { name: newName },
      select: {
        id: true,
        name: true,
      },
    });

    // Log the premium feature usage (simplified logging)
    console.log(`Premium feature used: hub_name_customization`, {
      userId: session.user.id,
      hubId: hubId,
      oldName: hub.name,
      newName: newName,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      hub: updatedHub,
      message: `Hub "${hub.name}" has been renamed to "${newName}"`,
    });

  } catch (error) {
    console.error("Error customizing hub name:", error);
    return NextResponse.json(
      { error: "Failed to customize hub name" },
      { status: 500 }
    );
  }
}

async function handleDELETE(request: NextRequest, { params }: Props) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await params;

    // Check if hub exists and user is the owner
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        name: true,
        ownerId: true,
      },
    });

    if (!hub) {
      return NextResponse.json(
        { error: "Hub not found" },
        { status: 404 }
      );
    }

    if (hub.ownerId !== session.user.id) {
      return NextResponse.json(
        { error: "Only hub owners can manage hub names" },
        { status: 403 }
      );
    }

    // For DELETE, we could implement reverting to original name if needed
    // For now, return method not allowed since direct name changes don't need "removal"
    return NextResponse.json(
      { error: "Method not supported for direct name changes" },
      { status: 405 }
    );

  } catch (error) {
    console.error("Error removing custom hub name:", error);
    return NextResponse.json(
      { error: "Failed to remove custom hub name" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handlers
export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.UPDATE,
  customMessage: "Hub name customization rate limit exceeded. Please wait before updating again.",
});

export const DELETE = withStrictRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.UPDATE,
  customMessage: "Hub name customization rate limit exceeded. Please wait before updating again.",
});
