import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { hubRecommendationService } from '@/lib/services/HubRecommendationService';
import { withAuthRateLimit } from '@/lib/rate-limit-middleware';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';

/**
 * Enhanced Hub Recommendations API
 * Provides personalized hub recommendations to address user retention crisis
 *
 * GET /api/hubs/recommendations
 * Query parameters:
 * - type: 'personalized' | 'trending' | 'activity' | 'similar' (default: 'personalized')
 * - limit: number (default: 8, max: 20)
 */
async function handleGET(request: NextRequest) {
  try {
    const session = await auth();
    const searchParams = request.nextUrl.searchParams;

    const type = searchParams.get('type') || 'personalized';
    const limit = Math.min(parseInt(searchParams.get('limit') || '8', 10), 20);

    let recommendations;

    switch (type) {
      case 'personalized':
        if (!session?.user?.id) {
          // Fall back to trending for unauthenticated users
          recommendations = await hubRecommendationService.getTrendingHubs(limit);
        } else {
          recommendations = await hubRecommendationService.getPersonalizedRecommendations(session.user.id);
        }
        break;

      case 'trending':
        recommendations = await hubRecommendationService.getTrendingHubs(limit);
        break;

      case 'activity':
        recommendations = await hubRecommendationService.getActivityBasedRecommendations(limit);
        break;

      case 'similar':
        if (!session?.user?.id) {
          return NextResponse.json(
            { error: 'Authentication required for similar recommendations' },
            { status: 401 }
          );
        }
        recommendations = await hubRecommendationService.getSimilarHubs(session.user.id, limit);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid recommendation type' },
          { status: 400 }
        );
    }

    // Transform recommendations for frontend consumption
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const transformedRecommendations = recommendations.map((rec: any) => ({
      hubId: rec.hubId,
      hub: {
        id: rec.hub.id,
        name: rec.hub.name,
        description: rec.hub.description,
        iconUrl: rec.hub.iconUrl,
        bannerUrl: rec.hub.bannerUrl,
        verified: rec.hub.verified,
        partnered: rec.hub.partnered,
        language: rec.hub.language,
        region: rec.hub.region,
        nsfw: rec.hub.nsfw,
        messageCount: rec.hub.messageCount,
        createdAt: rec.hub.createdAt,
        lastActive: rec.hub.lastActive,
        tags: rec.hub.tags || [],
        // Add activity indicators for better UX
        activityLevel: getActivityLevelFromHub(rec.hub),
        connectionCount: rec.hub._count?.connections || 0,
        recentMessageCount: rec.hub._count?.messages || 0,
        upvoteCount: rec.hub._count?.upvotes || 0,
      },
      score: rec.score,
      reason: rec.reason,
      // Add engagement metrics for frontend display
      engagementMetrics: {
        isHighActivity: (rec.hub._count?.messages || 0) >= 50,
        isGrowing: (rec.hub._count?.connections || 0) >= 5,
        isQuality: (rec.hub._count?.upvotes || 0) >= 5,
        isTrusted: rec.hub.verified || rec.hub.partnered,
      },
    }));

    return NextResponse.json({
      recommendations: transformedRecommendations,
      metadata: {
        type,
        count: transformedRecommendations.length,
        generatedAt: new Date().toISOString(),
        // Add user context for analytics
        userContext: session?.user?.id ? {
          authenticated: true,
          userId: session.user.id,
        } : {
          authenticated: false,
        },
      },
    });

  } catch (error) {
    console.error('Error fetching hub recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recommendations' },
      { status: 500 }
    );
  }
}

/**
 * Invalidate user's recommendation cache
 * POST /api/hubs/recommendations/invalidate
 */
async function handlePOST() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await hubRecommendationService.invalidateUserCache(session.user.id);

    return NextResponse.json({
      success: true,
      message: 'Recommendation cache invalidated',
    });

  } catch (error) {
    console.error('Error invalidating recommendation cache:', error);
    return NextResponse.json(
      { error: 'Failed to invalidate cache' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to determine activity level from hub data
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getActivityLevelFromHub(hub: any): 'LOW' | 'MEDIUM' | 'HIGH' {
  const recentMessages = hub._count?.messages || 0;
  const activeConnections = hub._count?.connections || 0;

  // Determine activity level based on recent activity
  if (recentMessages >= 100 || activeConnections >= 10) {
    return 'HIGH';
  } else if (recentMessages >= 20 || activeConnections >= 5) {
    return 'MEDIUM';
  } else {
    return 'LOW';
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.RECOMMENDATIONS,
});

export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.RECOMMENDATIONS,
  customMessage: "Recommendation cache invalidation rate limit exceeded.",
});
