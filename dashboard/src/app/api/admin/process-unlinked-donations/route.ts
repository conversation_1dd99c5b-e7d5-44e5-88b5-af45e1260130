import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";

/**
 * Admin endpoint to process unlinked Ko-fi donations
 * This can be used to retroactively grant premium access to users who linked their accounts
 * after making Ko-fi donations
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // TODO: Add admin role check here
    // For now, any authenticated user can run this (should be restricted to admins)
    
    console.log(`[Admin] Processing unlinked Ko-fi donations - initiated by user ${session.user.id}`);

    // Find all unprocessed Ko-fi Supporter donations that now have linked users
    const unprocessedDonations = await prisma.donation.findMany({
      where: {
        processed: false,
        isSubscription: true,
        tierName: { not: null },
        email: { not: null },
        discordUserId: null, // Not yet linked to a Discord user
      },
      include: {
        discordUser: true
      }
    });

    console.log(`[Admin] Found ${unprocessedDonations.length} unprocessed donations`);

    let processedCount = 0;
    let premiumGrantedCount = 0;

    for (const donation of unprocessedDonations) {
      if (!donation.email) continue;

      // Try to find a Discord user with this email
      const linkedUser = await prisma.user.findFirst({
        where: { email: donation.email },
        select: { id: true, hasMediaPremium: true, mediaPremiumExpiresAt: true }
      });

      if (linkedUser) {
        console.log(`[Admin] Found linked user ${linkedUser.id} for donation ${donation.id}`);

        // Update donation to link to Discord user
        await prisma.donation.update({
          where: { id: donation.id },
          data: {
            discordUserId: linkedUser.id,
            processed: true
          }
        });

        processedCount++;

        // Grant premium access for Ko-fi Supporter tier
        if (donation.tierName?.toLowerCase() === 'supporter') {
          // Calculate expiration date (30 days from donation date or now, whichever is later)
          const donationDate = new Date(donation.kofiTimestamp);
          const thirtyDaysFromDonation = new Date(donationDate.getTime() + 30 * 24 * 60 * 60 * 1000);
          const now = new Date();
          const expiresAt = thirtyDaysFromDonation > now ? thirtyDaysFromDonation : new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

          // Check if user already has premium that expires later
          const shouldUpdatePremium = !linkedUser.hasMediaPremium || 
            !linkedUser.mediaPremiumExpiresAt || 
            linkedUser.mediaPremiumExpiresAt < expiresAt;

          if (shouldUpdatePremium) {
            await prisma.user.update({
              where: { id: linkedUser.id },
              data: {
                hasMediaPremium: true,
                mediaPremiumExpiresAt: expiresAt,
              }
            });

            premiumGrantedCount++;
            console.log(`[Admin] Granted premium access to user ${linkedUser.id} until ${expiresAt.toISOString()}`);
          } else {
            console.log(`[Admin] User ${linkedUser.id} already has premium access until ${linkedUser.mediaPremiumExpiresAt?.toISOString()}`);
          }
        }
      }
    }

    console.log(`[Admin] Processing complete - ${processedCount} donations processed, ${premiumGrantedCount} premium grants`);

    return NextResponse.json({
      message: "Unlinked donations processed successfully",
      totalDonations: unprocessedDonations.length,
      processedDonations: processedCount,
      premiumGranted: premiumGrantedCount,
      details: unprocessedDonations.map(d => ({
        id: d.id,
        email: d.email,
        amount: d.amount,
        currency: d.currency,
        tierName: d.tierName,
        kofiTimestamp: d.kofiTimestamp,
        processed: processedCount > 0
      }))
    });

  } catch (error) {
    console.error("[Admin] Error processing unlinked donations:", error);
    return NextResponse.json(
      { error: "Failed to process unlinked donations" },
      { status: 500 }
    );
  }
}

/**
 * Get statistics about unlinked donations
 */
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // TODO: Add admin role check here

    // Count unprocessed donations
    const unprocessedCount = await prisma.donation.count({
      where: {
        processed: false,
        email: { not: null },
        discordUserId: null,
      }
    });

    // Count potentially linkable donations (where email exists in users table)
    const potentiallyLinkable = await prisma.donation.count({
      where: {
        processed: false,
        email: { not: null },
        discordUserId: null,
        email: {
          in: await prisma.user.findMany({
            select: { email: true },
            where: { email: { not: null } }
          }).then(users => users.map(u => u.email).filter(Boolean) as string[])
        }
      }
    });

    // Get recent unprocessed donations
    const recentUnprocessed = await prisma.donation.findMany({
      where: {
        processed: false,
        email: { not: null },
        discordUserId: null,
      },
      select: {
        id: true,
        email: true,
        amount: true,
        currency: true,
        tierName: true,
        kofiTimestamp: true,
        fromName: true
      },
      orderBy: { kofiTimestamp: 'desc' },
      take: 10
    });

    return NextResponse.json({
      unprocessedCount,
      potentiallyLinkable,
      recentUnprocessed
    });

  } catch (error) {
    console.error("[Admin] Error fetching donation statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch donation statistics" },
      { status: 500 }
    );
  }
}
