import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "@/lib/prisma";

/**
 * Ko-fi webhook payload schema
 * Based on Ko-fi webhook documentation and real payload examples
 */
const kofiPayloadSchema = z.object({
  verification_token: z.string().uuid("Verification token must be a valid UUID"),
  message_id: z.string().uuid("Message ID must be a valid UUID"),
  timestamp: z.string().datetime("Timestamp must be a valid ISO-8601 datetime"),
  type: z.enum(["Donation", "Subscription", "Commission", "Shop Order"]),
  is_public: z.boolean(),
  from_name: z.string().min(1, "From name cannot be empty"),
  message: z.string().nullable().optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/, 'Amount must be in decimal format (e.g., "3.00")'),
  url: z.string().url("URL must be a valid URL").optional(),
  email: z.string().email("Email must be valid").optional(),
  currency: z.string().length(3, "Currency must be a 3-letter code"),
  is_subscription_payment: z.boolean().optional().default(false),
  is_first_subscription_payment: z.boolean().optional().default(false),
  kofi_transaction_id: z.string().uuid("Transaction ID must be a valid UUID"),
  shop_items: z.array(z.any()).nullable().optional(),
  tier_name: z.string().nullable().optional(),
  shipping: z
    .object({
      full_name: z.string(),
      street_address: z.string(),
      city: z.string(),
      state_or_province: z.string(),
      postal_code: z.string(),
      country: z.string(),
      country_code: z.string(),
      telephone: z.string().optional(),
    })
    .nullable()
    .optional(),
});

type KofiPayload = z.infer<typeof kofiPayloadSchema>;

/**
 * Schema for the Ko-fi webhook request body
 * Ko-fi sends data as form-encoded with a 'data' field containing JSON
 */
const kofiWebhookSchema = z.object({
  data: z.string().transform((str, ctx) => {
    try {
      const parsed = JSON.parse(str);
      const result = kofiPayloadSchema.safeParse(parsed);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Invalid Ko-fi payload structure",
          path: ["data"],
        });
        return z.NEVER;
      }
      return result.data;
    } catch {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Invalid JSON in data field",
        path: ["data"],
      });
      return z.NEVER;
    }
  }),
});

/**
 * Validate Ko-fi webhook verification token
 */
function validateKofiWebhookToken(providedToken: string, expectedToken: string): boolean {
  if (!providedToken || !expectedToken) {
    return false;
  }
  return providedToken === expectedToken;
}

/**
 * Check if Ko-fi payload represents a subscription
 */
function isKofiSubscription(payload: KofiPayload): boolean {
  return payload.type === "Subscription" || payload.is_subscription_payment === true;
}

/**
 * Check if Ko-fi payload is a first subscription payment
 */
function isFirstSubscriptionPayment(payload: KofiPayload): boolean {
  return payload.is_first_subscription_payment === true;
}

/**
 * Get Ko-fi membership tier name from payload
 */
function getKofiTierName(payload: KofiPayload): string | null {
  return payload.tier_name || null;
}

/**
 * Process Ko-fi donation and update user premium status
 */
async function processDonation(payload: KofiPayload): Promise<void> {
  const amount = parseFloat(payload.amount);
  const isSubscription = isKofiSubscription(payload);
  const isFirstPayment = isFirstSubscriptionPayment(payload);
  const tierName = getKofiTierName(payload);

  console.log(`Processing Ko-fi donation: ${amount} ${payload.currency} from ${payload.from_name}`);
  console.log(`Subscription: ${isSubscription}, First Payment: ${isFirstPayment}, Tier: ${tierName}`);

  // Store donation record
  await prisma.donation.create({
    data: {
      kofiTransactionId: payload.kofi_transaction_id,
      messageId: payload.message_id,
      amount,
      currency: payload.currency,
      fromName: payload.from_name,
      message: payload.message,
      email: payload.email,
      isPublic: payload.is_public,
      kofiTimestamp: new Date(payload.timestamp),
      kofiUrl: payload.url,
      isSubscription,
      isFirstPayment,
      tierName,
      // Note: discordUserId would need to be linked separately
      // This could be done through a user linking their Ko-fi account
      discordUserId: null,
    },
  });

  // Grant premium access for Ko-fi Supporter tier ($3/month)
  if (isSubscription && tierName?.toLowerCase() === "supporter") {
    console.log("Granting Ko-fi Supporter premium access");
    
    // For now, we can't link to a specific Discord user without additional linking mechanism
    // This would need to be implemented separately (e.g., user links Ko-fi account in dashboard)
    
    // TODO: Implement user linking mechanism
    // TODO: Grant media premium access to linked Discord user
    // TODO: Send notification to user about premium access
  }

  console.log("Successfully processed Ko-fi donation");
}

/**
 * Ko-fi webhook endpoint
 * Handles Ko-fi webhook requests for donation processing
 */
export async function POST(request: NextRequest) {
  try {
    console.log(`Processing Ko-fi webhook request from ${request.headers.get("x-forwarded-for")}`);

    // Get verification token from environment
    const expectedToken = process.env.KOFI_VERIFICATION_TOKEN;
    if (!expectedToken) {
      console.error("Ko-fi verification token not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 }
      );
    }

    // Ko-fi sends data as form-encoded with a 'data' field containing JSON
    const formData = await request.formData();
    const dataField = formData.get("data");

    if (!dataField || typeof dataField !== "string") {
      console.warn("Missing or invalid data field in Ko-fi webhook");
      return NextResponse.json(
        { error: "Missing data field" },
        { status: 400 }
      );
    }

    // Validate the webhook payload
    const validationResult = kofiWebhookSchema.safeParse({ data: dataField });
    if (!validationResult.success) {
      console.error("Ko-fi webhook validation failed:", validationResult.error);
      return NextResponse.json(
        { error: "Invalid webhook payload" },
        { status: 400 }
      );
    }

    const kofiPayload = validationResult.data.data;

    // Verify webhook token
    if (!validateKofiWebhookToken(kofiPayload.verification_token, expectedToken)) {
      console.warn("Ko-fi webhook token verification failed");
      return NextResponse.json(
        { error: "Invalid verification token" },
        { status: 401 }
      );
    }

    // Check for duplicate transaction
    const existingDonation = await prisma.donation.findUnique({
      where: { kofiTransactionId: kofiPayload.kofi_transaction_id },
    });

    if (existingDonation) {
      console.warn(`Duplicate Ko-fi transaction: ${kofiPayload.kofi_transaction_id}`);
      return NextResponse.json({ message: "Transaction already processed" });
    }

    // Process the donation
    await processDonation(kofiPayload);

    console.log("Successfully processed Ko-fi webhook");
    return NextResponse.json({ message: "Webhook processed successfully" });

  } catch (error) {
    console.error("Error processing Ko-fi webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
