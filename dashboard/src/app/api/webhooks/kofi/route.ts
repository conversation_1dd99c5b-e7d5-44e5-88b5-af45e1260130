import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import prisma from '@/lib/prisma';

/**
 * Ko-fi webhook payload schema
 * Based on Ko-fi webhook documentation and real payload examples
 */
const kofiPayloadSchema = z.object({
  verification_token: z.string().uuid('Verification token must be a valid UUID'),
  message_id: z.string().uuid('Message ID must be a valid UUID'),
  timestamp: z.string().datetime('Timestamp must be a valid ISO-8601 datetime'),
  type: z.enum(['Donation', 'Subscription', 'Commission', 'Shop Order']),
  is_public: z.boolean(),
  from_name: z.string().min(1, 'From name cannot be empty'),
  message: z.string().nullable().optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/, 'Amount must be in decimal format (e.g., "3.00")'),
  url: z.string().url('URL must be a valid URL').optional(),
  email: z.string().email('Email must be valid').optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  is_subscription_payment: z.boolean().optional().default(false),
  is_first_subscription_payment: z.boolean().optional().default(false),
  kofi_transaction_id: z.string().uuid('Transaction ID must be a valid UUID'),
  shop_items: z.array(z.any()).nullable().optional(),
  tier_name: z.string().nullable().optional(),
  shipping: z
    .object({
      full_name: z.string(),
      street_address: z.string(),
      city: z.string(),
      state_or_province: z.string(),
      postal_code: z.string(),
      country: z.string(),
      country_code: z.string(),
      telephone: z.string().optional(),
    })
    .nullable()
    .optional(),
});

type KofiPayload = z.infer<typeof kofiPayloadSchema>;

/**
 * Parse and validate Ko-fi webhook data
 */
function parseKofiWebhookData(dataField: string): KofiPayload | null {
  try {
    const parsed = JSON.parse(dataField);
    const result = kofiPayloadSchema.safeParse(parsed);
    if (!result.success) {
      console.error('Ko-fi payload validation failed:', result.error.issues);
      return null;
    }
    return result.data;
  } catch (error) {
    console.error('Failed to parse Ko-fi webhook JSON:', error);
    return null;
  }
}

/**
 * Validate Ko-fi webhook verification token
 */
function validateKofiWebhookToken(providedToken: string, expectedToken: string): boolean {
  if (!providedToken || !expectedToken) {
    return false;
  }
  return providedToken === expectedToken;
}

/**
 * Check if Ko-fi payload represents a subscription
 */
function isKofiSubscription(payload: KofiPayload): boolean {
  return payload.type === 'Subscription' || payload.is_subscription_payment === true;
}

/**
 * Check if Ko-fi payload is a first subscription payment
 */
function isFirstSubscriptionPayment(payload: KofiPayload): boolean {
  return payload.is_first_subscription_payment === true;
}

/**
 * Get Ko-fi membership tier name from payload
 */
function getKofiTierName(payload: KofiPayload): string | null {
  return payload.tier_name || null;
}

/**
 * Find linked Discord user by Ko-fi email
 */
async function findLinkedDiscordUser(kofiEmail: string): Promise<string | null> {
  if (!kofiEmail) return null;

  // Try to find user by email (if they've linked their Ko-fi email)
  const user = await prisma.user.findFirst({
    where: { email: kofiEmail },
    select: { id: true }
  });

  return user?.id || null;
}

/**
 * Grant media premium access to Discord user
 */
async function grantMediaPremiumAccess(
  userId: string,
  isSubscription: boolean,
  tierName: string | null
): Promise<void> {
  console.log(`Granting media premium access to user ${userId}`);

  // Calculate expiration date for subscription (30 days from now)
  const expiresAt = isSubscription ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null;

  await prisma.user.update({
    where: { id: userId },
    data: {
      hasMediaPremium: true,
      mediaPremiumExpiresAt: expiresAt, // null for permanent, date for subscription
    }
  });

  console.log(`Media premium granted to user ${userId}, expires: ${expiresAt?.toISOString() || 'never'}`);
}

/**
 * Send premium access notification (placeholder for future Discord bot integration)
 */
async function sendPremiumNotification(
  userId: string,
  tierName: string | null,
  isFirstPayment: boolean
): Promise<void> {
  console.log(`[Notification] User ${userId} granted premium access - Tier: ${tierName}, First: ${isFirstPayment}`);

  // TODO: Integrate with Discord bot to send DM or channel notification
  // This would require calling the Discord bot API or using a shared message queue
  // For now, we'll just log the notification

  const message = isFirstPayment
    ? `🎉 Welcome to Ko-fi ${tierName} tier! You now have unlimited media sharing in InterChat calls.`
    : `✨ Your Ko-fi ${tierName} subscription has been renewed! Media premium access continues.`;

  console.log(`[Notification] Would send to user ${userId}: ${message}`);
}

/**
 * Process Ko-fi donation and update user premium status
 */
async function processDonation(payload: KofiPayload): Promise<void> {
  const amount = parseFloat(payload.amount);
  const isSubscription = isKofiSubscription(payload);
  const isFirstPayment = isFirstSubscriptionPayment(payload);
  const tierName = getKofiTierName(payload);

  console.log(`Processing Ko-fi donation: ${amount} ${payload.currency} from ${payload.from_name}`);
  console.log(
    `Subscription: ${isSubscription}, First Payment: ${isFirstPayment}, Tier: ${tierName}`,
  );

  try {
    // Try to find linked Discord user
    const linkedUserId = await findLinkedDiscordUser(payload.email || '');

    // Store donation record
    const donation = await prisma.donation.create({
      data: {
        kofiTransactionId: payload.kofi_transaction_id,
        messageId: payload.message_id,
        amount,
        currency: payload.currency,
        fromName: payload.from_name,
        message: payload.message,
        email: payload.email,
        isPublic: payload.is_public,
        kofiTimestamp: new Date(payload.timestamp),
        kofiUrl: payload.url,
        isSubscription,
        isFirstPayment,
        tierName,
        discordUserId: linkedUserId,
        processed: false,
      },
    });

    console.log(`Donation record created with ID: ${donation.id}`);

    // Grant premium access for Ko-fi Supporter tier ($3/month)
    if (isSubscription && tierName?.toLowerCase() === 'supporter') {
      console.log('Ko-fi Supporter tier detected');

      if (linkedUserId) {
        console.log(`Granting premium access to linked user: ${linkedUserId}`);

        // Grant media premium access
        await grantMediaPremiumAccess(linkedUserId, isSubscription, tierName);

        // Send notification
        await sendPremiumNotification(linkedUserId, tierName, isFirstPayment);

        // Mark donation as processed
        await prisma.donation.update({
          where: { id: donation.id },
          data: { processed: true }
        });

        console.log(`Premium benefits granted and donation marked as processed`);
      } else {
        console.log(`No linked Discord user found for email: ${payload.email || 'none'}`);
        console.log(`Donation stored but premium benefits not granted - user needs to link account`);
      }
    } else {
      // Mark non-premium donations as processed
      await prisma.donation.update({
        where: { id: donation.id },
        data: { processed: true }
      });
    }

    console.log('Successfully processed Ko-fi donation');
  }
  catch (error) {
    console.error('Error processing donation:', error);
    throw error;
  }
}

/**
 * Ko-fi webhook endpoint
 * Handles Ko-fi webhook requests for donation processing
 */
export async function POST(request: NextRequest) {
  try {
    console.log(`Processing Ko-fi webhook request from ${request.headers.get('x-forwarded-for')}`);

    // Verify content type (Ko-fi sends application/x-www-form-urlencoded)
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('application/x-www-form-urlencoded')) {
      console.warn(`Invalid content type: ${contentType}. Expected application/x-www-form-urlencoded`);
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
    }

    // Get verification token from environment
    const expectedToken = process.env.KOFI_VERIFICATION_TOKEN;
    if (!expectedToken) {
      console.error('Ko-fi verification token not configured');
      return NextResponse.json({ error: 'Service configuration error' }, { status: 500 });
    }

    // Ko-fi sends data as application/x-www-form-urlencoded with a 'data' field containing JSON
    const formData = await request.formData();
    const dataField = formData.get('data');

    if (!dataField || typeof dataField !== 'string') {
      console.warn('Missing or invalid data field in Ko-fi webhook');
      return NextResponse.json({ error: 'Missing data field' }, { status: 400 });
    }

    // Parse and validate the webhook payload
    const kofiPayload = parseKofiWebhookData(dataField);
    if (!kofiPayload) {
      return NextResponse.json({ error: 'Invalid webhook payload structure' }, { status: 400 });
    }

    // Verify webhook token
    if (!validateKofiWebhookToken(kofiPayload.verification_token, expectedToken)) {
      console.warn('Ko-fi webhook token verification failed');
      return NextResponse.json({ error: 'Invalid verification token' }, { status: 401 });
    }

    // Check for duplicate transaction
    const existingDonation = await prisma.donation.findUnique({
      where: { kofiTransactionId: kofiPayload.kofi_transaction_id },
    });

    if (existingDonation) {
      console.warn(`Duplicate Ko-fi transaction: ${kofiPayload.kofi_transaction_id}`);
      return NextResponse.json({ message: 'Transaction already processed' });
    }

    // Process the donation
    await processDonation(kofiPayload);

    console.log('Successfully processed Ko-fi webhook');
    return NextResponse.json({ message: 'Webhook processed successfully' });
  }
  catch (error) {
    console.error('Error processing Ko-fi webhook:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
