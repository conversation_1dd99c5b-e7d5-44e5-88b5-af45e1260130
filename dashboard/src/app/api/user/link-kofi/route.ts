import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

/**
 * Schema for linking Ko-fi account
 */
const linkKofiSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

/**
 * Link user's Ko-fi account by email
 * This allows automatic premium benefit granting when Ko-fi webhooks are received
 */
async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validationResult = linkKofiSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid email address",
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;
    const userId = session.user.id;

    // Check if email is already linked to another user
    const existingUser = await prisma.user.findFirst({
      where: {
        email: email,
        id: { not: userId } // Exclude current user
      },
      select: { id: true }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "This email is already linked to another Discord account" },
        { status: 409 }
      );
    }

    // Update user's email for Ko-fi linking
    await prisma.user.update({
      where: { id: userId },
      data: { email: email }
    });

    // Check for any existing unprocessed Ko-fi donations with this email
    const unprocessedDonations = await prisma.donation.findMany({
      where: {
        email: email,
        processed: false,
        discordUserId: null,
        isSubscription: true,
        tierName: { not: null }
      }
    });

    let premiumGranted = false;

    // Process any existing Ko-fi Supporter donations
    for (const donation of unprocessedDonations) {
      if (donation.tierName?.toLowerCase() === 'supporter') {
        console.log(`Processing existing Ko-fi Supporter donation for user ${userId}`);

        // Grant media premium access
        const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
        await prisma.user.update({
          where: { id: userId },
          data: {
            hasMediaPremium: true,
            mediaPremiumExpiresAt: expiresAt,
          }
        });

        // Update donation record
        await prisma.donation.update({
          where: { id: donation.id },
          data: {
            discordUserId: userId,
            processed: true
          }
        });

        premiumGranted = true;
        console.log(`Premium access granted to user ${userId} from existing donation`);
      }
    }

    return NextResponse.json({
      message: "Ko-fi account linked successfully",
      email: email,
      premiumGranted: premiumGranted,
      processedDonations: unprocessedDonations.length
    });

  } catch (error) {
    console.error("Error linking Ko-fi account:", error);
    return NextResponse.json(
      { error: "Failed to link Ko-fi account" },
      { status: 500 }
    );
  }
}

/**
 * Get current Ko-fi linking status
 */
async function handleGET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        email: true,
        hasMediaPremium: true,
        mediaPremiumExpiresAt: true,
      }
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check for any Ko-fi donations linked to this user
    const kofiDonations = await prisma.donation.findMany({
      where: { discordUserId: session.user.id },
      select: {
        id: true,
        amount: true,
        currency: true,
        tierName: true,
        isSubscription: true,
        kofiTimestamp: true,
        processed: true
      },
      orderBy: { kofiTimestamp: 'desc' },
      take: 5 // Last 5 donations
    });

    return NextResponse.json({
      isLinked: !!user.email,
      email: user.email,
      hasMediaPremium: user.hasMediaPremium,
      mediaPremiumExpiresAt: user.mediaPremiumExpiresAt,
      recentDonations: kofiDonations
    });

  } catch (error) {
    console.error("Error fetching Ko-fi status:", error);
    return NextResponse.json(
      { error: "Failed to fetch Ko-fi status" },
      { status: 500 }
    );
  }
}

/**
 * Unlink Ko-fi account
 */
async function handleDELETE() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Remove email link (but keep premium status if active)
    await prisma.user.update({
      where: { id: session.user.id },
      data: { email: null }
    });

    return NextResponse.json({
      message: "Ko-fi account unlinked successfully"
    });

  } catch (error) {
    console.error("Error unlinking Ko-fi account:", error);
    return NextResponse.json(
      { error: "Failed to unlink Ko-fi account" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to all endpoints
export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});

export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USERS.GET_PROFILE,
});

export const DELETE = withAuthRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});
