import { auth } from '@/auth';
import { discordEmailVerificationService } from '@/lib/discord-email-verification';
import prisma from '@/lib/prisma';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';
import { withAuthRateLimit } from '@/lib/rate-limit-middleware';
import { NextResponse } from 'next/server';

/**
 * Link user's Ko-fi account using verified Discord email
 * This prevents fraud by using Discord OAuth to verify email authenticity
 */
async function handlePOST() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Verify and link Discord email using OAuth
    const verificationResult =
      await discordEmailVerificationService.verifyAndLinkDiscordEmail(userId);

    if (!verificationResult.success) {
      return NextResponse.json(
        { error: verificationResult.error || 'Failed to verify Discord email' },
        { status: 400 },
      );
    }

    console.log(
      `Ko-fi account linked for user ${userId} with verified Discord email: ${verificationResult.email}`,
    );

    // Process any existing Ko-fi donations with this verified email
    const donationResult = await discordEmailVerificationService.processExistingDonations(
      userId,
      verificationResult.email!,
    );

    return NextResponse.json({
      message: 'Ko-fi account linked successfully using verified Discord email',
      email: verificationResult.email,
      premiumGranted: donationResult.premiumGranted,
      donationsProcessed: donationResult.processed,
      isVerified: true,
    });
  } catch (error) {
    console.error('Error linking Ko-fi account:', error);
    return NextResponse.json({ error: 'Failed to link Ko-fi account' }, { status: 500 });
  }
}

/**
 * Get current Ko-fi linking status
 */
async function handleGET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        email: true,
        hasMediaPremium: true,
        mediaPremiumExpiresAt: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check for any Ko-fi donations linked to this user
    const kofiDonations = await prisma.donation.findMany({
      where: { discordUserId: session.user.id },
      select: {
        id: true,
        amount: true,
        currency: true,
        tierName: true,
        isSubscription: true,
        kofiTimestamp: true,
        processed: true,
      },
      orderBy: { kofiTimestamp: 'desc' },
      take: 5, // Last 5 donations
    });

    return NextResponse.json({
      isLinked: !!user.email,
      email: user.email,
      isVerified: !!user.email, // Email is verified through Discord OAuth
      hasMediaPremium: user.hasMediaPremium,
      mediaPremiumExpiresAt: user.mediaPremiumExpiresAt,
      recentDonations: kofiDonations,
    });
  } catch (error) {
    console.error('Error fetching Ko-fi status:', error);
    return NextResponse.json({ error: 'Failed to fetch Ko-fi status' }, { status: 500 });
  }
}

/**
 * Unlink Ko-fi account
 */
async function handleDELETE() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Remove email link (but keep premium status if active)
    await prisma.user.update({
      where: { id: session.user.id },
      data: { email: null },
    });

    return NextResponse.json({
      message: 'Ko-fi account unlinked successfully',
    });
  } catch (error) {
    console.error('Error unlinking Ko-fi account:', error);
    return NextResponse.json({ error: 'Failed to unlink Ko-fi account' }, { status: 500 });
  }
}

// Apply rate limiting to all endpoints
export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});

export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USERS.GET_PROFILE,
});

export const DELETE = withAuthRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});
