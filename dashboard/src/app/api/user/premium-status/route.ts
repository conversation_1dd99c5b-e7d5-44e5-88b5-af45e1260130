import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit";

/**
 * Check if user has Ko-fi Supporter tier (premium status)
 * This endpoint provides premium status information for frontend components
 */
async function checkPremiumStatus(userId: string): Promise<boolean> {
  try {
    // Check if user has active media premium (Ko-fi Supporter tier grants this)
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        hasMediaPremium: true,
        mediaPremiumExpiresAt: true,
      },
    });

    if (!user?.hasMediaPremium) return false;

    // If no expiration date, it's permanent premium
    if (!user.mediaPremiumExpiresAt) return true;

    // Check if premium hasn't expired
    return user.mediaPremiumExpiresAt > new Date();
  } catch (error) {
    console.error("Error checking premium status:", error);
    return false;
  }
}

async function handleGET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const hasPremium = await checkPremiumStatus(session.user.id);

    return NextResponse.json({
      hasPremium,
      canCustomizeHubName: hasPremium,
      hasMediaPremium: hasPremium, // Ko-fi Supporter tier grants media premium
      supporterTier: hasPremium ? "supporter" : null,
    });
  } catch (error) {
    console.error("Error fetching premium status:", error);
    return NextResponse.json(
      { error: "Failed to fetch premium status" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handler
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USER.PROFILE,
});
