"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
// No need for Hub type import anymore

export default function CreateHubPage() {
  const [step, setStep] = useState(1);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(true);
  const [welcomeMessage, setWelcomeMessage] = useState("");
  const [rules, setRules] = useState<string[]>([""]); // Start with one empty rule
  const [nameError, setNameError] = useState("");
  const [isValidatingName, setIsValidatingName] = useState(false);
  const [isNameValid, setIsNameValid] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const nameInputRef = useRef<HTMLInputElement>(null);

  // Focus on name input when component mounts
  useEffect(() => {
    if (nameInputRef.current) {
      nameInputRef.current.focus();
    }
  }, []);

  // Check if hub name is unique
  const checkHubName = async (name: string) => {
    if (name.length < 3) {
      setIsNameValid(false);
      return;
    }

    setIsValidatingName(true);
    setIsNameValid(false);

    try {
      // Use the dedicated validation endpoint that checks both public and private hubs
      const response = await fetch(
        `/api/hubs/validate-name?name=${encodeURIComponent(name)}`
      );
      if (!response.ok) {
        setIsValidatingName(false);
        return;
      }

      const data = await response.json();

      if (!data.available) {
        setNameError("This hub name is already taken");
        setIsNameValid(false);
      } else {
        setNameError("");
        setIsNameValid(true);
      }
    } catch (error) {
      console.error("Error checking hub name:", error);
      setIsNameValid(false);
    } finally {
      setIsValidatingName(false);
    }
  };

  // Add a new rule field
  const addRule = () => {
    setRules([...rules, ""]);
  };

  // Update a rule at a specific index
  const updateRule = (index: number, value: string) => {
    const updatedRules = [...rules];
    updatedRules[index] = value;
    setRules(updatedRules);
  };

  // Remove a rule at a specific index
  const removeRule = (index: number) => {
    if (rules.length === 1) {
      // If it's the last rule, just clear it instead of removing
      updateRule(0, "");
      return;
    }

    const updatedRules = rules.filter((_, i) => i !== index);
    setRules(updatedRules);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    if (name.length < 3) {
      toast({
        title: "Error",
        description: "Hub name must be at least 3 characters",
        variant: "destructive",
      });
      return;
    }

    if (description.length < 10) {
      toast({
        title: "Error",
        description: "Description must be at least 10 characters",
        variant: "destructive",
      });
      return;
    }

    if (nameError || !isNameValid || isValidatingName) {
      toast({
        title: "Error",
        description:
          nameError || "Please ensure the hub name is valid and unique",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out empty rules
      const filteredRules = rules.filter((rule) => rule.trim() !== "");

      const response = await fetch("/api/hubs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
          private: isPrivate,
          welcomeMessage: welcomeMessage || null,
          rules: filteredRules,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create hub");
      }

      const data = await response.json();

      toast({
        title: "Hub Created",
        description: "Your hub has been created successfully.",
      });

      // Redirect to the hub management page
      router.push(`/dashboard/hubs/${data.hub.id}`);
    } catch (error) {
      console.error("Error creating hub:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to create hub",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (
      step === 1 &&
      name.length >= 3 &&
      !nameError &&
      isNameValid &&
      !isValidatingName
    ) {
      setStep(2);
    } else if (step === 2 && description.length >= 10) {
      setStep(3);
    }
  };

  const prevStep = () => {
    if (step === 2) {
      setStep(1);
    } else if (step === 3) {
      setStep(2);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (
        step === 1 &&
        name.length >= 3 &&
        !nameError &&
        isNameValid &&
        !isValidatingName
      ) {
        nextStep();
      } else if (step === 2 && description.length >= 10) {
        nextStep();
      }
    }
  };

  // Check hub name uniqueness when name changes
  useEffect(() => {
    // Reset validation state when name changes
    if (name.length < 3) {
      setIsNameValid(false);
      setNameError("");
    }

    const timer = setTimeout(() => {
      if (name.length >= 3) {
        checkHubName(name);
      }
    }, 500); // Debounce to avoid too many requests

    return () => clearTimeout(timer);
  }, [name]);

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" size="sm" className="mr-2" asChild>
          <Link href="/dashboard/hubs">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create New Hub</h1>
      </div>
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm overflow-hidden">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Create Your Hub</CardTitle>
            <CardDescription>
              {step === 1
                ? "Let's start with a unique name for your hub"
                : step === 2
                  ? "Tell people what your hub is about"
                  : "Set up welcome message and rules (optional)"}
            </CardDescription>
          </CardHeader>

          {/* Step indicator */}
          <div className="px-6 pb-2">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-400">
                Step {step} of 3
              </div>
              <div className="text-sm text-gray-500">
                {step === 1
                  ? "Hub Name"
                  : step === 2
                    ? "Description"
                    : "Welcome & Rules"}
              </div>
            </div>
            <div className="w-full bg-gray-800/50 h-1 rounded-full overflow-hidden">
              <div
                className="bg-gradient-to-r from-blue-600 to-indigo-600 h-full transition-all duration-300 ease-in-out"
                style={{ width: `${(step / 3) * 100}%` }}
              />
            </div>
          </div>

          {step === 1 ? (
            <CardContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-base font-medium">
                  What&apos;s your hub called?
                </Label>
                <Input
                  id="name"
                  ref={nameInputRef}
                  placeholder="Enter a name like 'Gaming Community' or 'Art Club'"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  onKeyDown={handleKeyDown}
                  required
                  minLength={3}
                  maxLength={32}
                  className={`bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 text-lg py-6 ${nameError ? "border-red-500" : isNameValid ? "border-green-500" : ""}`}
                />
                <div className="flex justify-between">
                  <div className="flex items-center gap-1">
                    {isValidatingName ? (
                      <>
                        <svg
                          className="animate-spin h-3 w-3 text-indigo-400"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <p className="text-xs text-indigo-400">
                          Checking availability...
                        </p>
                      </>
                    ) : isNameValid && !nameError ? (
                      <>
                        <svg
                          className="h-3 w-3 text-green-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <p className="text-xs text-green-400">
                          Name is available
                        </p>
                      </>
                    ) : (
                      <p
                        className={`text-xs ${nameError ? "text-red-400" : "text-gray-400"}`}
                      >
                        {nameError ||
                          "Choose a unique name between 3-32 characters"}
                      </p>
                    )}
                  </div>
                  <p className="text-xs text-gray-400">{name.length}/32</p>
                </div>
              </div>
              <div className="pt-4">
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={
                    name.length < 3 ||
                    nameError !== "" ||
                    isValidatingName ||
                    !isNameValid
                  }
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none py-6 text-base"
                >
                  Continue
                </Button>
              </div>
            </CardContent>
          ) : step === 2 ? (
            <CardContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="description" className="text-base font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  placeholder="Briefly describe what your hub is about"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  onKeyDown={handleKeyDown}
                  required
                  minLength={10}
                  maxLength={500}
                  className="min-h-[80px] bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
                />
                <div className="flex justify-between">
                  <p className="text-xs text-gray-400">
                    A short description helps people understand your hub&apos;s
                    purpose (min 10 characters)
                  </p>
                  <p className="text-xs text-gray-400">
                    {description.length}/500
                  </p>
                </div>
              </div>

              <div className="bg-gray-800/30 p-4 rounded-lg border border-gray-800/50 mt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="private" className="text-base font-medium">
                      Private Hub
                    </Label>
                    <p className="text-sm text-gray-400">
                      Only visible to members, requires an invite to join
                    </p>
                  </div>
                  <Switch
                    id="private"
                    checked={isPrivate}
                    onCheckedChange={setIsPrivate}
                    className="data-[state=checked]:bg-indigo-600"
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex-1 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={description.length < 10}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
                >
                  Continue
                </Button>
              </div>
            </CardContent>
          ) : (
            <CardContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="welcomeMessage" className="text-base font-medium">
                  Welcome Message (Optional)
                </Label>
                <Textarea
                  id="welcomeMessage"
                  placeholder="Welcome message for new members"
                  value={welcomeMessage}
                  onChange={(e) => setWelcomeMessage(e.target.value)}
                  maxLength={1000}
                  className="min-h-[100px] bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 resize-y whitespace-pre-wrap"
                />
                <p className="text-xs text-gray-400">
                  This message will be shown to new members when they join your
                  hub. You can use these variables: <code className="bg-gray-800 px-1 rounded">{'{'} user {'}'}</code>, <code className="bg-gray-800 px-1 rounded">{'{'} hubName {'}'}</code>, <code className="bg-gray-800 px-1 rounded">{'{'} serverName {'}'}</code>, <code className="bg-gray-800 px-1 rounded">{'{'} memberCount {'}'}</code>, <code className="bg-gray-800 px-1 rounded">{'{'} totalConnections {'}'}</code>
                </p>
              </div>

              <div className="space-y-2 mt-6">
                <div className="flex items-center justify-between">
                  <Label htmlFor="rules" className="text-base font-medium">
                    Hub Rules (Optional)
                  </Label>
                  <Button
                    type="button"
                    onClick={addRule}
                    variant="outline"
                    size="sm"
                    className="h-8 px-2 text-xs border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                  >
                    Add Rule
                  </Button>
                </div>
                <div className="space-y-3 mt-2">
                  {rules.map((rule, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <Textarea
                        placeholder={`Rule ${index + 1}`}
                        value={rule}
                        onChange={(e) => updateRule(index, e.target.value)}
                        className="flex-1 bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[60px] resize-y"
                      />
                      <Button
                        type="button"
                        onClick={() => removeRule(index)}
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400 hover:text-red-400 hover:bg-red-500/10"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M18 6L6 18" />
                          <path d="M6 6l12 12" />
                        </svg>
                      </Button>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Rules help set expectations for your hub members
                </p>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex-1 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Hub"
                  )}
                </Button>
              </div>
            </CardContent>
          )}
        </form>
      </Card>
    </div>
  );
}
