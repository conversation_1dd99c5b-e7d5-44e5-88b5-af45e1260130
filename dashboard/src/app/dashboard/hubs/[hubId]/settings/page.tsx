import { auth } from "@/auth";
import { HubSettingsForm } from "@/components/dashboard/hubs/hub-settings-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { ArrowLeft, AlertCircle } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface HubSettingsPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubSettingsPage({
  params,
}: HubSettingsPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/settings`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      private: true,
      settings: true,
    },
  });

  if (!hub) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            {hub.name} Settings
          </h1>
        </div>
      </div>
      <HubNavigationTabs
        hubId={hubId}
        currentTab="settings"
        canModerate={canModerate}
        canEdit={canEdit}
      />
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Hub Settings</CardTitle>
          <CardDescription>
            Configure hub settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          {canEdit ? (
            <HubSettingsForm
              hubId={hubId}
              initialSettings={hub.settings || 0}
            />
          ) : (
            <div className="flex items-center justify-center p-6">
              <AlertCircle className="h-5 w-5 mr-2 text-amber-500" />
              <p className="text-gray-400">
                You don&apos;t have permission to edit this hub.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
