import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Shield, AlertTriangle, Ban } from "lucide-react";
import Link from "next/link";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import prisma from "@/lib/prisma";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";

export default async function HubModerationPage({
  params,
}: {
  params: Promise<{ hubId: string }>;
}) {
  const session = await auth();
  if (!session?.user) {
    redirect("/login");
  }

  const { hubId } = await params;

  // Check if user has permission to view this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  // Only allow moderators and managers to access this page
  if (permissionLevel < PermissionLevel.MODERATOR) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Get hub details
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      private: true,
      locked: true,
      antiSwearRules: {
        select: {
          id: true,
          name: true,
        },
      },
      infractions: {
        select: {
          id: true,
          status: true,
        },
      },
    },
  });

  if (!hub) {
    redirect("/dashboard/hubs");
  }

  // Hub data is now ready to use

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            {hub.name} Moderation
          </h1>
        </div>
      </div>
      <HubNavigationTabs
        hubId={hubId}
        currentTab="moderation"
        canModerate={true}
        canEdit={permissionLevel >= PermissionLevel.MANAGER}
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Ban className="h-5 w-5 mr-2 text-red-400" />
              Blacklist Management
            </CardTitle>
            <CardDescription>
              Manage blacklisted users and servers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              Blacklist users or servers to prevent them from joining this hub.
              {hub.infractions.length > 0 && (
                <span className="block mt-2 text-sm">
                  <span className="font-semibold text-red-400">
                    {
                      hub.infractions.filter((i) => i.status === "ACTIVE")
                        .length
                    }
                  </span>{" "}
                  active blacklist entries
                </span>
              )}
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
            >
              <Link href={`/dashboard/moderation/blacklist?hubId=${hubId}`}>
                <Shield className="h-4 w-4 mr-2" />
                Manage Blacklist
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-amber-400" />
              Infractions
            </CardTitle>
            <CardDescription>View and manage user infractions</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              View a history of all infractions issued in this hub.
              {hub.infractions.length > 0 && (
                <span className="block mt-2 text-sm">
                  <span className="font-semibold text-amber-400">
                    {hub.infractions.length}
                  </span>{" "}
                  active infractions
                </span>
              )}
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-600/80 hover:to-orange-600/80 border-none"
            >
              <Link href={`/dashboard/hubs/${hubId}/infractions`}>
                <AlertTriangle className="h-4 w-4 mr-2" />
                View Infractions
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-purple-400" />
              Anti-Swear
            </CardTitle>
            <CardDescription>Configure anti-swear settings</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              Set up and manage anti-swear rules for this hub.
              {hub.antiSwearRules.length > 0 && (
                <span className="block mt-2 text-sm">
                  <span className="font-semibold text-purple-400">
                    {hub.antiSwearRules.length}
                  </span>{" "}
                  anti-swear rules configured
                </span>
              )}
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-600/80 hover:to-indigo-600/80 border-none"
            >
              <Link href={`/dashboard/hubs/${hubId}/anti-swear`}>
                <Shield className="h-4 w-4 mr-2" />
                Configure Anti-Swear
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
