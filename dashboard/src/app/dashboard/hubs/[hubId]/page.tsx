import { auth } from "@/auth";
import { HubNavigationTabs } from "@/components/dashboard/hubs/hub-navigation-tabs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import {
  ArrowLeft,
  Clock,
  Edit,
  Globe,
  Home,
  Lock,
  MessageSquare,
  PlusCircle,
  Shield,
  Users
} from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface HubPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export async function generateMetadata({
  params,
}: HubPageProps): Promise<Metadata> {
  const { hubId } = await params;
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: { name: true },

  });

  return {
    title: hub
      ? `${hub.name} | InterChat Dashboard`
      : "Hub | InterChat Dashboard",
    description: "Manage your InterChat hub",
  };
}

export default async function HubPage({ params }: HubPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      connections: {
        orderBy: { lastActive: "desc" },
        select: {
          id: true,
          serverId: true,
          connected: true,
          compact: true,
          createdAt: true,
          lastActive: true,
          server: true,
        }
      },
      moderators: { include: { user: true } },
      upvotes: true,
      owner: true,
    },

  });

  if (!hub) {
    notFound();
  }

  const createdAt = new Date(hub.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const lastActive = hub.lastActive
    ? formatDistanceToNow(new Date(hub.lastActive), { addSuffix: true })
    : "Never";

  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;

  // Get some additional stats
  const activeConnectionsCount = hub.connections.filter(
    (conn) => conn.connected,
  ).length;
  const totalConnectionsCount = hub.connections.length;

  // Get total infractions count
  const infractionsCount = await prisma.infraction.count({
    where: { hubId },
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/hubs">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight truncate max-w-[180px] sm:max-w-[250px] md:max-w-none">
            {hub.name}
          </h1>
        </div>
        <div className="flex items-center gap-2 mt-4 sm:mt-0 w-full sm:w-auto">
          {canEdit && (
            <Button
              asChild
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none flex-1 sm:flex-initial"
            >
              <Link href={`/dashboard/hubs/${hubId}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Hub
              </Link>
            </Button>
          )}
        </div>
      </div>
      <HubNavigationTabs
        hubId={hubId}
        currentTab="overview"
        canModerate={canModerate}
        canEdit={permissionLevel >= PermissionLevel.MANAGER}
      />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Hub Info Card */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm lg:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-3">
              {canEdit ? (
                <Link href={`/dashboard/hubs/${hubId}/edit`} className="group relative">
                  <div className="h-16 w-16 rounded-full border-2 border-gray-700/50 overflow-hidden flex-shrink-0 transition-all duration-200 group-hover:border-gray-600/70 group-hover:scale-105">
                    <Image
                      src={hub.iconUrl}
                      alt={hub.name}
                      width={64}
                      height={64}
                      className="object-cover transition-all duration-200 group-hover:brightness-75"
                      style={{ width: "100%", height: "100%", objectFit: "cover" }}
                    />
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-gray-800/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <Edit className="h-5 w-5 text-white" />
                    </div>
                  </div>
                </Link>
              ) : (
                <div className="h-16 w-16 rounded-full border-2 border-gray-700/50 overflow-hidden flex-shrink-0">
                  <Image
                    src={hub.iconUrl}
                    alt={hub.name}
                    width={64}
                    height={64}
                    className="object-cover"
                    style={{ width: "100%", height: "100%", objectFit: "cover" }}
                  />
                </div>
              )}
              <div>
                <div className="flex items-center gap-2">
                  <CardTitle className="text-xl">{hub.name}</CardTitle>
                  {hub.private ? (
                    <Lock className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Globe className="h-4 w-4 text-green-400" />
                  )}
                </div>
                <CardDescription className="line-clamp-2">
                  {hub.description}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-1">
                  <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                    <Users className="h-3 w-3 text-yellow-400" />
                  </div>
                  Owner
                </span>
                <span className="text-gray-200">
                  {hub.owner?.name || "Unknown"}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-1">
                  <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                    <Home className="h-3 w-3 text-blue-400" />
                  </div>
                  Connections
                </span>
                <span className="text-gray-200">
                  {activeConnectionsCount} active / {totalConnectionsCount}{" "}
                  total
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-1">
                  <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                    <Shield className="h-3 w-3 text-purple-400" />
                  </div>
                  Moderators
                </span>
                <span className="text-gray-200">{hub.moderators.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center gap-1">
                  <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                    <MessageSquare className="h-3 w-3 text-indigo-400" />
                  </div>
                  Created
                </span>
                <span className="text-gray-200">{createdAt}</span>
              </div>
              {canModerate && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-400 flex items-center gap-1">
                    <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                      <Shield className="h-3 w-3 text-red-400" />
                    </div>
                    Infractions
                  </span>
                  <span className="text-gray-200">{infractionsCount}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Hub Stats Card */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Hub Stats</CardTitle>
            <CardDescription>Current hub statistics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <div className="h-2 w-2 rounded-full bg-green-400"></div>
                </div>
                Status
              </span>
              <span className="text-green-400">Active</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Clock className="h-3 w-3 text-blue-400" />
                </div>
                Last Active
              </span>
              <span className="text-gray-200">{lastActive}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Users className="h-3 w-3 text-yellow-400" />
                </div>
                Upvotes
              </span>
              <span className="text-gray-200">{hub.upvotes.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Shield className="h-3 w-3 text-purple-400" />
                </div>
                Your Role
              </span>
              <span
                className={cn(
                  "px-2 py-0.5 rounded-full text-xs border",
                  permissionLevel === PermissionLevel.OWNER
                    ? "bg-yellow-900/30 text-yellow-400 border-yellow-700/30"
                    : permissionLevel === PermissionLevel.MANAGER
                      ? "bg-blue-900/30 text-blue-400 border-blue-700/30"
                      : "bg-purple-900/30 text-purple-400 border-purple-700/30",
                )}
              >
                {permissionLevel === PermissionLevel.OWNER
                  ? "Owner"
                  : permissionLevel === PermissionLevel.MANAGER
                    ? "Manager"
                    : "Moderator"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Connections */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <CardTitle>Recent Connections</CardTitle>
                <CardDescription>
                  Recently active server connections
                </CardDescription>
              </div>
              <Button
                asChild
                variant="outline"
                size="sm"
                className="w-full md:w-auto border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                <Link href={`/dashboard/hubs/${hubId}/connections`}>
                  <Home className="h-4 w-4 mr-2" />
                  View All
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {hub.connections.length > 0 ? (
              <div className="space-y-3">
                {hub.connections.slice(0, 6).map((connection) => (
                  <div
                    key={connection.id}
                    className="flex items-center justify-between p-3 rounded-md bg-gray-900/50 border border-gray-800/50"
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-md bg-gray-800/80 flex items-center justify-center">
                        <Home className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <div className="font-medium">
                          {connection.server?.name || "Unknown Server"}
                        </div>
                        <div className="flex items-center text-xs text-gray-400">
                          <div
                            className={`h-1.5 w-1.5 rounded-full ${
                              connection.connected
                                ? "bg-green-400"
                                : "bg-gray-500"
                            } mr-1.5`}
                          ></div>
                          {connection.connected ? "Connected" : "Paused"}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatDistanceToNow(new Date(connection.lastActive), {
                        addSuffix: true,
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Home className="h-12 w-12 mx-auto text-gray-500 mb-4" />
                <p className="text-gray-400 mb-4">
                  No connections have been added yet.
                </p>
                {canEdit && (
                  <Button
                    asChild
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
                  >
                    <Link href={`/hubs/${hubId}`}>
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Connection
                    </Link>
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Hub Members */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <CardTitle>Hub Members</CardTitle>
                <CardDescription>Moderators and managers</CardDescription>
              </div>
              <Button
                asChild
                variant="outline"
                size="sm"
                className="w-full md:w-auto border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                <Link href={`/dashboard/hubs/${hubId}/members`}>
                  <Users className="h-4 w-4 mr-2" />
                  View All
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full border-2 border-yellow-500/20 overflow-hidden">
                    <Image
                      src={
                        hub.owner?.image ||
                        "https://api.dicebear.com/7.x/shapes/svg?seed=owner"
                      }
                      alt={hub.owner?.name || "Owner"}
                      width={32}
                      height={32}
                      className="object-cover"
                      style={{ width: "100%", height: "100%" }}
                    />
                  </div>
                  <div>
                    <div className="font-medium">
                      {hub.owner?.name || "Unknown"}
                    </div>
                    <div className="text-xs text-gray-400">Owner</div>
                  </div>
                </div>
              </div>

              {hub.moderators.length > 0 ? (
                <div className="space-y-2">
                  {hub.moderators.map((mod) => (
                    <div
                      key={mod.id}
                      className="flex items-center justify-between p-3 rounded-md bg-gray-900/50 border border-gray-800/50"
                    >
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full border-2 border-purple-500/20 overflow-hidden">
                          <Image
                            src={
                              mod.user?.image ||
                              "https://api.dicebear.com/7.x/shapes/svg?seed=mod"
                            }
                            alt={mod.user?.name || "Moderator"}
                            width={32}
                            height={32}
                            className="object-cover"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div>
                          <div className="font-medium">{mod.user?.name}</div>
                          <div className="flex items-center text-xs text-gray-400">
                            <Shield className="h-3 w-3 mr-1" />
                            {mod.role === "MANAGER" ? "Manager" : "Moderator"}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-400">
                    No moderators or managers have been added yet.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
