"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { trpc } from "@/utils/trpc";
import { formatDistanceToNow, format } from "date-fns";
import { Bell, Calendar, Clock } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function AnnouncementsPage() {
  // Fetch all announcements
  const { data, isLoading } = trpc.announcement.getAnnouncements.useQuery();
  const announcements = data?.announcements || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Announcements</h1>
        <Button
          asChild
          variant="outline"
          className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
        >
          <Link href="/dashboard">
            Back to Dashboard
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={`skeleton-${i}`} className="border-gray-800 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 bg-gray-800" />
                <Skeleton className="h-4 w-1/2 bg-gray-800 mt-2" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full bg-gray-800" />
                <Skeleton className="h-4 w-full bg-gray-800" />
                <Skeleton className="h-4 w-3/4 bg-gray-800" />
                <Skeleton className="h-32 w-full bg-gray-800 rounded-md" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : announcements.length === 0 ? (
        <Card className="border-gray-800 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Bell className="h-12 w-12 text-gray-500 mb-4" />
            <h3 className="text-xl font-medium text-gray-300 mb-2">No Announcements</h3>
            <p className="text-gray-400 text-center max-w-md">
              There are no announcements to display at this time. Check back later for updates.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {announcements.map((announcement) => (
            <Card 
              key={announcement.id} 
              className={`border-gray-800 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm ${
                announcement.isUnread ? "border-l-4 border-l-indigo-500" : ""
              }`}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                    {announcement.title}
                  </CardTitle>
                  {announcement.isUnread && (
                    <div className="px-2 py-1 bg-indigo-900/30 border border-indigo-500/30 rounded-full text-xs text-indigo-300">
                      New
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-400 mt-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {format(new Date(announcement.createdAt), "MMM d, yyyy")}
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatDistanceToNow(new Date(announcement.createdAt), { addSuffix: true })}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-300 whitespace-pre-line">
                  {announcement.content}
                </p>
                {announcement.thumbnailUrl && (
                  <div className="mt-4">
                    <Image
                      width={500}
                      height={500}
                      src={announcement.thumbnailUrl}
                      alt=""
                      className="w-full h-auto rounded-md border border-gray-700/50"
                    />
                  </div>
                )}
                {announcement.imageUrl && (
                  <div className="mt-4">
                    <Image
                      width={500}
                      height={500}
                      src={announcement.imageUrl}
                      alt=""
                      className="w-full h-auto rounded-md border border-gray-700/50"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
