"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AppealWithInfraction, useMyAppeals } from "@/hooks/use-appeals";
import { formatDistanceToNow } from "date-fns";
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Check,
  Clock,
  MessageCircle,
  Shield,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export default function MyAppealsPage() {
  // Pagination
  const [page, setPage] = useState(1);
  const itemsPerPage = 10;

  // Fetch appeals using Tanstack Query
  const {
    data,
    isLoading: loading,
    isError,
    error: queryError,
    refetch
  } = useMyAppeals(page, itemsPerPage);

  // Extract data
  const appeals = data?.appeals || [];
  const totalPages = data ? Math.ceil(data.total / itemsPerPage) || 1 : 1;

  // Format error message
  const error = isError
    ? queryError instanceof Error
      ? queryError.message
      : "Failed to fetch appeals"
    : null;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">My Appeals</h1>
      </div>

      {/* Appeals List */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">My Appeal Requests</CardTitle>
          <CardDescription>
            View the status of appeals you&apos;ve submitted
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <AppealSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Error Loading Appeals
              </h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          ) : appeals.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="h-12 w-12 mx-auto text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Appeals Found</h3>
              <p className="text-gray-400 mb-4">
                You haven&apos;t submitted any appeals yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {appeals.map((appeal) => (
                <MyAppealCard key={appeal.id} appeal={appeal} />
              ))}
            </div>
          )}
        </CardContent>
        {!loading && appeals.length > 0 && (
          <CardFooter className="flex justify-between border-t border-gray-800 pt-4">
            <div className="text-sm text-gray-400">
              Page {page} of {totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}

interface MyAppealCardProps {
  appeal: AppealWithInfraction;
}

function MyAppealCard({ appeal }: MyAppealCardProps) {
  const createdAt = formatDistanceToNow(new Date(appeal.createdAt), {
    addSuffix: true,
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "ACCEPTED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/10 text-green-500 border-green-500/20"
          >
            <Check className="h-3 w-3 mr-1" />
            Accepted
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/10 text-red-500 border-red-500/20"
          >
            <X className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col p-4 rounded-md bg-gray-900/50 border border-gray-800/50">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0 h-10 w-10 rounded-md overflow-hidden">
            <Image
              src={appeal.infraction.hub.iconUrl || "/images/default-hub.png"}
              alt={appeal.infraction.hub.name}
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <div>
            <div className="font-medium">
              Appeal for {appeal.infraction.hub.name}
            </div>
            <div className="text-xs text-gray-400">Submitted {createdAt}</div>
          </div>
        </div>
        <div>{getStatusBadge(appeal.status)}</div>
      </div>
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-300 mb-1">
          Your Appeal Reason:
        </h4>
        <p className="text-sm text-gray-300">{appeal.reason}</p>
      </div>
      <div className="bg-gray-800/30 p-3 rounded-md mb-3 border border-gray-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-gray-400" />
          <h4 className="text-sm font-medium">Infraction Details</h4>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">ID:</span>
          <Link
            href={`/dashboard/infractions/${appeal.infractionId}`}
            className="text-xs text-blue-400 hover:underline"
          >
            {appeal.infractionId}
          </Link>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">Type:</span>
          <span className="text-xs">
            {appeal.infraction.type === "BLACKLIST" ? "Blacklist" : "Warning"}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">Reason:</span>
          <span className="text-xs truncate">{appeal.infraction.reason}</span>
        </div>
      </div>
      {appeal.status === "REJECTED" && (
        <div className="p-3 rounded-md bg-red-950/20 border border-red-900/30">
          <p className="text-sm text-red-400">
            Your appeal has been rejected. You may contact the hub moderators
            for more information.
          </p>
        </div>
      )}
      {appeal.status === "ACCEPTED" && (
        <div className="p-3 rounded-md bg-green-950/20 border border-green-900/30">
          <p className="text-sm text-green-400">
            Your appeal has been accepted. The infraction has been appealed.
          </p>
        </div>
      )}
    </div>
  );
}

function AppealSkeleton() {
  return (
    <div className="flex flex-col p-4 rounded-md bg-gray-900/50 border border-gray-800/50">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-md" />
          <div>
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24 mt-1" />
          </div>
        </div>
        <Skeleton className="h-6 w-20" />
      </div>

      <div className="mb-3">
        <Skeleton className="h-4 w-32 mb-1" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4 mt-1" />
      </div>

      <div className="bg-gray-900/30 p-3 rounded-md mb-3">
        <div className="flex items-center gap-2 mb-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-3/4 mb-1" />
        <Skeleton className="h-4 w-5/6" />
      </div>
    </div>
  );
}
