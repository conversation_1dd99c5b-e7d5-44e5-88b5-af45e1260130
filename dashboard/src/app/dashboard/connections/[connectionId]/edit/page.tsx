"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Trash } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import { ConnectionNavigationTabs } from "@/components/dashboard/connections/connection-navigation-tabs";
import { ConnectionEditForm } from "@/components/dashboard/connections/connection-edit-form";
import { ConnectionErrorBoundary } from "@/components/dashboard/connections/connection-error-boundary";

export default function ConnectionEditPage() {
  const { connectionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<{ message: string; status?: number } | null>(null);
  const [connection, setConnection] = useState<
    (Connection & { hub: Hub; server: ServerData }) | null
  >(null);
  const { toast } = useToast();
  const router = useRouter();

  // Fetch connection data
  const fetchConnection = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/dashboard/connections/${connectionId}`,
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to fetch connection data";

        // Handle specific error cases
        if (response.status === 403) {
          throw new Error("You don't have permission to access this connection. You need 'Manage Channels' permission on the Discord server.");
        } else if (response.status === 404) {
          throw new Error("Connection not found. It may have been deleted or you may not have access to it.");
        } else if (response.status === 429) {
          throw new Error("Rate limit exceeded. Please wait a moment before trying again.");
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setConnection(data.connection);
    } catch (error) {
      console.error("Error fetching connection:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load connection data";

      let status: number | undefined;
      if (errorMessage.includes("Rate limit") || errorMessage.includes("429")) {
        status = 429;
      } else if (errorMessage.includes("permission") || errorMessage.includes("Forbidden")) {
        status = 403;
      } else if (errorMessage.includes("not found")) {
        status = 404;
      }

      setError({
        message: errorMessage,
        status,
      });
    } finally {
      setIsLoading(false);
    }
  }, [connectionId]);

  useEffect(() => {
    if (connectionId) {
      fetchConnection();
    }
  }, [connectionId, fetchConnection]);

  const handleConnectionUpdate = (updatedConnection: Connection & { hub: Hub; server: ServerData }) => {
    setConnection(updatedConnection);
  };

  const handleSaveComplete = () => {
    router.push(`/dashboard/connections/${connectionId}`);
  };

  const handleRetry = () => {
    fetchConnection();
  };

  const handleDelete = async () => {
    if (
      !confirm(
        "Are you sure you want to delete this connection? This action cannot be undone.",
      )
    ) {
      return;
    }

    try {
      setIsDeleting(true);

      const response = await fetch(
        `/api/dashboard/connections/${connectionId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete connection");
      }

      toast({
        title: "Connection Deleted",
        description: "The connection has been deleted successfully.",
      });

      router.push("/dashboard/servers?tab=connections");
    } catch (error) {
      console.error("Error deleting connection:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete connection",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <ConnectionErrorBoundary
        error={error}
        onRetry={handleRetry}
        isRetrying={isLoading}
      />
    );
  }

  if (!connection) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Connection Not Found</h2>
          <p className="text-gray-400 mb-4">The connection you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.</p>
          <Button asChild>
            <Link href="/dashboard/servers?tab=connections">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Connections
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/servers?tab=connections">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Connections
            </Link>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Edit Connection</h1>
            <p className="text-sm text-gray-400 mt-1">Configure connection settings and preferences</p>
          </div>
        </div>
        <Button
          variant="destructive"
          size="sm"
          onClick={handleDelete}
          disabled={isDeleting}
          className="px-6"
        >
          {isDeleting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Deleting...
            </>
          ) : (
            <>
              <Trash className="h-4 w-4 mr-2" />
              Delete Connection
            </>
          )}
        </Button>
      </div>
      <ConnectionNavigationTabs connectionId={connectionId as string} currentTab="edit" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
        <div className="md:col-span-2">
          <ConnectionEditForm
            connection={connection}
            onUpdate={handleConnectionUpdate}
            onSave={handleSaveComplete}
            isSaving={isSaving}
            setSaving={setIsSaving}
          />
        </div>

        <div className="space-y-4 sm:space-y-6">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-4 sm:px-6">
              <CardTitle>Hub</CardTitle>
              <CardDescription>
                The hub this connection links to
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 sm:px-6">
              <div className="flex items-center gap-3">
                <Image
                  src={connection?.hub.iconUrl || "/pfp1.png"}
                  alt={connection?.hub.name || "Hub"}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium">{connection?.hub.name}</div>
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0"
                    asChild
                  >
                    <Link href={`/hubs/${connection?.hub.id}`} target="_blank">
                      View Hub
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-4 sm:px-6">
              <CardTitle>Server</CardTitle>
              <CardDescription>
                The Discord server this connection links to
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 sm:px-6">
              <div className="flex items-center gap-3">
                <Image
                  src={`https://api.dicebear.com/7.x/identicon/svg?seed=${connection?.server.id}`}
                  alt={connection?.server.name || "Server"}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium">{connection?.server.name}</div>
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0"
                    asChild
                  >
                    <Link href={`/dashboard/servers/${connection?.server.id}`}>
                      View Server
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
