"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Info, ScrollText } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import RulesSection from "./RulesSection";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export default function MainContent({ hub }: { hub: SimplifiedHub }) {
  const { description } = hub;

  return (
    <div className="lg:col-span-2 space-y-6">
      <Tabs defaultValue="about" className="w-full">
        <TabsList className="bg-gray-900/50 border border-gray-800">
          <TabsTrigger
            value="about"
            className="data-[state=active]:bg-gray-800"
          >
            <Info className="w-4 h-4 mr-2" />
            About
          </TabsTrigger>
          <TabsTrigger
            value="rules"
            className="data-[state=active]:bg-gray-800"
          >
            <ScrollText className="w-4 h-4 mr-2" />
            Rules
          </TabsTrigger>
        </TabsList>

        <TabsContent value="about" className="mt-6">
          <Card className="bg-[#0f1117] border-gray-800">
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-4">About this hub</h3>
              <div className="prose prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {description}
                </ReactMarkdown>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="mt-6">
          <RulesSection hub={hub} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
