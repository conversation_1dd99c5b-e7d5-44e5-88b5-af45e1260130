// faq
import { Accordion, Accordions } from "fumadocs-ui/components/accordion";
import { <PERSON>R<PERSON>, HelpCircle } from "lucide-react";
import Link from "next/link";
import { Button } from "./button";

export function FaqSection() {
  return (
    <section className="relative overflow-hidden py-32 bg-gradient-to-b from-gray-950 to-gray-900" id="faq">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-800/20 via-transparent to-transparent" />
      </div>
      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-20 relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center justify-center bg-gray-800/50 text-white px-6 py-3 rounded-full border border-gray-700/50 backdrop-blur-lg shadow-md">
              <HelpCircle className="w-5 h-5 mr-3 text-primary" aria-hidden="true" />
              <span className="font-medium tracking-wide text-base">
                Frequently Asked Questions
              </span>
            </div>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white">
            Everything About
            <span className="block mt-2 md:mt-3 text-primary">
              InterChat
            </span>
          </h2>

          <p className="max-w-3xl mx-auto text-lg text-gray-300">
            Everything you need to know about connecting your Discord
            communities with InterChat. Find answers to common questions about
            setup, features, and moderation.
          </p>
        </div>

        <div className="group relative">
          <div
            className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/20 to-primary-alt/20 blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-700"
            style={{ willChange: "opacity" }}
          />

          <div className="flex flex-col bg-gray-800/50 backdrop-blur-xl rounded-2xl p-1.5 border border-gray-700/50 hover:border-gray-600/70 transition-all duration-300 shadow-xl hover:shadow-primary/10 relative">
            <div className="p-6 lg:p-8">
              <Accordions
                type="single"
                collapsible
                className="w-full text-start divide-y divide-gray-700/50"
              >
                {faqs.map((faq) => (
                  <div
                    key={faq.id}
                    itemScope // Added for Schema
                    itemProp="mainEntity" // Added for Schema
                    itemType="https://schema.org/Question" // Added for Schema
                  >
                    <Accordion title={faq.title} id={faq.id}>
                      <div
                        className="p-4 bg-gray-700/30 rounded-lg text-gray-300"
                        itemScope
                        itemProp="acceptedAnswer"
                        itemType="https://schema.org/Answer"
                      >
                        <div itemProp="text">{faq.content}</div>
                      </div>
                    </Accordion>
                  </div>
                ))}
              </Accordions>
            </div>
          </div>
        </div>

        <div className="mt-20 text-center relative z-10">
          <Button
            variant="ghost"
            className="text-primary hover:text-white px-0 group/button hover:bg-gray-800/50 rounded-lg"
            asChild
          >
            <Link
              href="/docs"
              className="flex items-center justify-center px-6 py-3"
              aria-label="View All InterChat Documentation"
            >
              <span className="text-primary font-medium group-hover/button:text-white transition-colors duration-300">
                View All InterChat Documentation
              </span>
              <div className="ml-2 p-1.5 rounded-full bg-gray-700/50 group-hover/button:bg-gray-600/50 transition-all duration-300">
                <ArrowRight
                  className="w-4 h-4 text-primary group-hover/button:text-white transition-all duration-300 transform group-hover/button:translate-x-1"
                  aria-hidden="true"
                />
              </div>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

const faqs = [
  {
    id: "what-is-interchat",
    title: "What is InterChat?",
    content: (
      <>
        InterChat is a Discord bot that makes multi-server messaging possible.
        It allows you to link a channel in your server to a &quot;hub&quot; (or
        group chat) where you can chat with other servers that have also linked
        their channels. Discover how InterChat can transform your Discord
        community.
      </>
    ),
  },
  {
    id: "how-do-i-get-started",
    title: "How do I get started with InterChat?",
    content: (
      <>
        To get started, invite the bot to your server and use the /setup command
        to link a channel to a hub. You can then start chatting with other
        servers that have also linked their channels. For more information,
        check out the{" "}
        <Link
          href="/docs"
          className="text-purple-700 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium"
        >
          documentation
        </Link>
        . Explore the power of InterChat and unlock the potential of
        cross-server communication.
      </>
    ),
  },
  {
    id: "what-is-a-hub",
    title: "What is an InterChat hub?",
    content: (
      <>
        A hub is an InterChat feature that acts similar to a traditional
        &quot;group chat&quot;. Servers can link their channels to it and talk.
        When you link a channel to a hub, you can chat with other servers that
        have also linked their channels to the same hub. Hubs can be public or
        private, and you can create your own hub or join an existing one. Find
        awesome community made hubs using the{" "}
        <Link
          href="/hubs"
          className="text-purple-700 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium"
        >
          Hub Browser
        </Link>
        ! Join an InterChat hub today and connect with like-minded communities.
      </>
    ),
  },
  {
    id: "is-interchat-free-to-use",
    title: "Is InterChat free to use?",
    content: (
      <>
        InterChat is a completely free and open-source project that is developed
        by the community for the community. If you would like to support the
        project, you can do so by contributing to the codebase, reporting bugs,
        or suggesting new features. You can also support the project by becoming
        a sponsor on GitHub or donating to the project on{" "}
        <Link
          href="/donate"
          className="text-purple-700 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium"
        >
          Ko-Fi
        </Link>{" "}
        to help us continue to provide a free and open-source solution for
        Discord community connectivity.
      </>
    ),
  },
  {
    id: "advanced-features",
    title: "How do I learn more about InterChat?",
    content: (
      <>
        You can learn more about advanced features by checking out the{" "}
        <Link
          href="/docs"
          className="text-purple-700 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 font-medium"
        >
          documentation
        </Link>
        . We worked hard to make sure the commands are easy to understand and
        act as a guide to help you get started. Unlock the full potential of
        InterChat with our comprehensive documentation.
      </>
    ),
  },
];
