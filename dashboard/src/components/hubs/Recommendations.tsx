"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useHubRecommendations,
  useMultipleRecommendations,
  getActivityLevelColor,
  getActivityLevelDescription,
  formatRecommendationReason,
  type RecommendationType
} from '@/hooks/use-hub-recommendations';
import {
  Flame,
  TrendingUp,
  Users,
  MessageCircle,
  Heart,
  Shield,
  Star,
  Clock,
  ArrowRight
} from 'lucide-react';

interface RecommendationsProps {
  type?: RecommendationType;
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

/**
 * Enhanced Hub Recommendations Component
 * Displays personalized hub recommendations with activity indicators
 * Addresses user retention by showing engaging, relevant communities
 */
export function Recommendations({
  type = 'personalized',
  limit = 8,
  showHeader = true,
  className = '',
}: RecommendationsProps) {
  const { recommendations, isLoading, metadata } = useHubRecommendations(type, limit);

  if (isLoading) {
    return <RecommendationsSkeleton />;
  }

  if (recommendations.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500">No recommendations available at the moment.</p>
        <p className="text-sm text-gray-400 mt-2">
          Try joining some hubs to get personalized recommendations!
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">
            {getRecommendationTitle(type)}
          </h2>
          <p className="text-gray-400">
            {getRecommendationDescription(type)}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {recommendations.map((recommendation, index) => (
          <RecommendationCard
            key={recommendation.hubId}
            recommendation={recommendation}
            position={index}
            type={type}
          />
        ))}
      </div>

      {metadata && (
        <div className="mt-4 text-xs text-gray-500 text-center">
          Updated {new Date(metadata.generatedAt).toLocaleTimeString()}
        </div>
      )}
    </div>
  );
}

/**
 * Multi-section recommendations for the main hubs page
 */
export function MultiSectionRecommendations() {
  const sections = [
    { type: 'personalized' as const, limit: 6 },
    { type: 'trending' as const, limit: 4 },
    { type: 'activity' as const, limit: 4 },
  ];

  const { results, isLoading } = useMultipleRecommendations(sections);

  if (isLoading) {
    return (
      <div className="space-y-8">
        {sections.map((section) => (
          <div key={section.type}>
            <Skeleton className="h-8 w-64 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: section.limit }).map((_, i) => (
                <Skeleton key={i} className="h-48 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {results.map((result) => (
        <Recommendations
          key={result.type}
          type={result.type}
          limit={sections.find(s => s.type === result.type)?.limit}
          showHeader={true}
          className="mb-8"
        />
      ))}
    </div>
  );
}

/**
 * Individual recommendation card component
 */
function RecommendationCard({
  recommendation,
  position,
  type,
}: {
  recommendation: {
    hub: {
      id: string;
      name: string;
      description: string;
      iconUrl: string;
      verified: boolean;
      partnered: boolean;
      activityLevel: 'LOW' | 'MEDIUM' | 'HIGH';
      connectionCount: number;
      recentMessageCount: number;
      lastActive: string | Date;
      tags: { name: string }[];
    };
    reason: string;
    engagementMetrics: {
      isHighActivity: boolean;
      isGrowing: boolean;
      isQuality: boolean;
    };
  };
  position: number;
  type: RecommendationType;
}) {
  const { hub, reason, engagementMetrics } = recommendation;
  const reasonFormatted = formatRecommendationReason(reason);

  return (
    <Card className="bg-gray-800 border-gray-700 hover:border-gray-600 transition-all duration-200 hover:shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Image
              src={hub.iconUrl}
              alt={hub.name}
              width={48}
              height={48}
              className="w-12 h-12 rounded-lg object-cover"
            />
            <div className="flex-1 min-w-0">
              <CardTitle className="text-white text-lg truncate">
                {hub.name}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <ActivityIndicator level={hub.activityLevel} />
                {hub.verified && <Shield className="w-4 h-4 text-blue-400" />}
                {hub.partnered && <Star className="w-4 h-4 text-yellow-400" />}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <CardDescription className="text-gray-300 text-sm mb-4 line-clamp-2">
          {hub.description}
        </CardDescription>

        {/* Recommendation reason */}
        <div className="mb-4">
          <p className="text-sm font-medium text-blue-400">
            {reasonFormatted.primary}
          </p>
          {reasonFormatted.secondary && (
            <p className="text-xs text-gray-400 mt-1">
              {reasonFormatted.secondary}
            </p>
          )}
        </div>

        {/* Engagement metrics */}
        <div className="flex flex-wrap gap-2 mb-4">
          {engagementMetrics.isHighActivity && (
            <Badge variant="secondary" className="text-xs">
              <Flame className="w-3 h-3 mr-1" />
              Very Active
            </Badge>
          )}
          {engagementMetrics.isGrowing && (
            <Badge variant="secondary" className="text-xs">
              <TrendingUp className="w-3 h-3 mr-1" />
              Growing
            </Badge>
          )}
          {engagementMetrics.isQuality && (
            <Badge variant="secondary" className="text-xs">
              <Heart className="w-3 h-3 mr-1" />
              Well-rated
            </Badge>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <Users className="w-3 h-3 mr-1" />
              {hub.connectionCount}
            </span>
            <span className="flex items-center">
              <MessageCircle className="w-3 h-3 mr-1" />
              {hub.recentMessageCount}
            </span>
          </div>
          <span className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {getRelativeTime(typeof hub.lastActive === 'string' ? hub.lastActive : hub.lastActive.toISOString())}
          </span>
        </div>

        {/* Tags */}
        {hub.tags && hub.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {hub.tags.slice(0, 3).map((tag: { name: string }) => (
              <Badge key={tag.name} variant="outline" className="text-xs">
                {tag.name}
              </Badge>
            ))}
            {hub.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{hub.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Action button */}
        <Link href={`/hubs/${hub.id}`}>
          <Button
            className="w-full cursor-pointer"
            size="sm"
            onClick={() => {
              // TODO: Track recommendation click for analytics
              console.log('Recommendation clicked:', { hubId: hub.id, type, position });
            }}
          >
            View Hub
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </Link>
      </CardContent>
    </Card>
  );
}

/**
 * Activity level indicator component
 */
function ActivityIndicator({ level }: { level: 'LOW' | 'MEDIUM' | 'HIGH' }) {
  const color = getActivityLevelColor(level);
  const description = getActivityLevelDescription(level);

  return (
    <span className={`text-xs font-medium ${color}`}>
      {description}
    </span>
  );
}

/**
 * Loading skeleton for recommendations
 */
function RecommendationsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <Card key={i} className="bg-gray-800 border-gray-700">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <Skeleton className="w-12 h-12 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-32 mb-2" />
                <Skeleton className="h-4 w-20" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-4" />
            <div className="flex gap-2 mb-4">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-8 w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Utility functions
function getRecommendationTitle(type: RecommendationType): string {
  switch (type) {
    case 'personalized':
      return 'Recommended for You';
    case 'trending':
      return 'Trending Hubs';
    case 'activity':
      return 'Most Active Today';
    case 'similar':
      return 'Similar to Your Favorites';
    case 'friends':
      return 'Where Your Friends Are';
    default:
      return 'Hub Recommendations';
  }
}

function getRecommendationDescription(type: RecommendationType): string {
  switch (type) {
    case 'personalized':
      return 'Discover communities that match your interests and activity preferences';
    case 'trending':
      return 'Popular hubs with growing communities and active discussions';
    case 'activity':
      return 'Hubs with the highest activity and engagement today';
    case 'similar':
      return 'Find new communities similar to the ones you already love';
    case 'friends':
      return 'Join hubs where your friends are already active';
    default:
      return 'Explore new communities and connect with like-minded people';
  }
}

function getRelativeTime(date: string): string {
  const now = new Date();
  const past = new Date(date);
  const diffMs = now.getTime() - past.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays}d ago`;
  } else if (diffHours > 0) {
    return `${diffHours}h ago`;
  } else {
    return 'Recently';
  }
}
