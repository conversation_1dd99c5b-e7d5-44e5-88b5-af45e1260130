"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { useRouter } from 'next/navigation';
import { Search, ArrowRight, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { HubSearchDropdown } from '@/app/hubs/components/hub-search-dropdown';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  /** Initial search term */
  initialValue?: string;
  /** Placeholder text */
  placeholder?: string;
  /** Size variant */
  size?: 'default' | 'large';
  /** Style variant */
  variant?: 'hero' | 'header';
  /** Whether to show the search button */
  showButton?: boolean;
  /** Custom className */
  className?: string;
  /** Callback when search is performed */
  onSearch?: (term: string) => void;
  /** Callback when tag is selected */
  onTagSelect?: (tag: string) => void;
  /** Whether to auto-focus on mount */
  autoFocus?: boolean;
}

/**
 * Enhanced Search Bar Component
 * Features dropdown functionality with search suggestions, popular tags, and recent searches
 */
export function SearchBar({
  initialValue = '',
  placeholder = 'Search for gaming, art, anime, technology hubs...',
  size = 'default',
  variant = 'header',
  showButton = true,
  className,
  onSearch,
  onTagSelect,
  autoFocus = false,
}: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
        setIsSearchFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const term = searchTerm.trim();
    if (term) {
      if (onSearch) {
        onSearch(term);
      } else {
        router.push(`/hubs/search?search=${encodeURIComponent(term)}`);
      }
      setShowDropdown(false);
      setIsSearchFocused(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setShowDropdown(value.length > 0 || isSearchFocused);
  };

  const handleInputFocus = () => {
    setIsSearchFocused(true);
    setShowDropdown(true);
  };

  const handleTagSelect = (tag: string) => {
    if (onTagSelect) {
      onTagSelect(tag);
    } else {
      router.push(`/hubs/search?tags=${encodeURIComponent(tag)}`);
    }
    setShowDropdown(false);
    setIsSearchFocused(false);
  };

  const handleDropdownSearch = (term: string) => {
    if (onSearch) {
      onSearch(term);
    } else {
      router.push(`/hubs/search?search=${encodeURIComponent(term)}`);
    }
    setShowDropdown(false);
    setIsSearchFocused(false);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setShowDropdown(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Size-based styling
  const sizeClasses = {
    default: {
      container: 'h-12',
      input: 'text-base',
      button: 'px-6',
      icon: 'w-5 h-5',
    },
    large: {
      container: 'h-14',
      input: 'text-lg',
      button: 'px-8',
      icon: 'w-6 h-6',
    },
  };

  // Variant-based styling
  const variantClasses = {
    hero: {
      container: 'bg-gray-800/50 backdrop-blur-xl border-gray-700/50 focus-within:border-blue-500/50',
      input: 'bg-transparent border-0 placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0',
      button: 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105',
      wrapper: 'relative group',
      glow: 'absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300',
    },
    header: {
      container: 'bg-gray-800/50 border-gray-700 focus-within:border-blue-500',
      input: 'bg-transparent border-0 text-white placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0',
      button: 'bg-blue-600 hover:bg-blue-700 text-white',
      wrapper: 'relative',
      glow: '',
    },
  };

  const currentSize = sizeClasses[size];
  const currentVariant = variantClasses[variant];

  return (
    <div ref={containerRef} className={cn('relative', className)}>
      <form onSubmit={handleSearch} className="w-full">
        <div className={currentVariant.wrapper}>
          {/* Glow effect for hero variant */}
          {variant === 'hero' && <div className={currentVariant.glow} />}

          <div className={cn(
            'relative flex items-center rounded-2xl p-2 transition-all duration-300',
            currentVariant.container,
            currentSize.container
          )}>
            {/* Search Icon */}
            <div className="flex items-center pl-4">
              <Search className={cn(
                'transition-colors duration-300',
                isSearchFocused ? 'text-blue-400' : 'text-gray-400',
                currentSize.icon
              )} />
            </div>

            {/* Input Field */}
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={searchTerm}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              className={cn(
                'flex-1',
                currentVariant.input,
                currentSize.input
              )}
            />

            {/* Clear Button */}
            {searchTerm && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="mr-2 h-8 w-8 p-0 text-gray-400 hover:text-gray-200"
              >
                <X className="h-4 w-4" />
              </Button>
            )}

            {/* Search Button */}
            {showButton && (
              <Button
                type="submit"
                size={size === 'large' ? 'lg' : 'default'}
                className={cn(currentVariant.button, currentSize.button)}
                disabled={!searchTerm.trim()}
              >
                Search
                <ArrowRight className={cn('ml-2', currentSize.icon)} />
              </Button>
            )}
          </div>
        </div>
      </form>

      {/* Search Dropdown */}
      <AnimatePresence>
        {showDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 z-50"
          >
            <HubSearchDropdown
              searchTerm={searchTerm}
              onTagSelect={handleTagSelect}
              onSearch={handleDropdownSearch}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
