"use client";

import React from 'react';
import { Recommendations } from './Recommendations';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Sparkles, TrendingUp, Activity } from 'lucide-react';
import { useInvalidateRecommendations } from '@/hooks/use-hub-recommendations';

/**
 * Client-side recommendations section for the hubs page
 * Integrates enhanced recommendations with the existing server-rendered content
 */
export function RecommendationsSection() {
  const { invalidate, isInvalidating } = useInvalidateRecommendations();

  return (
    <div className="space-y-12">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-white mb-2 flex items-center">
            <Sparkles className="w-8 h-8 mr-3 text-blue-400" />
            Discover Communities
          </h2>
          <p className="text-gray-400 text-lg">
            Find active communities that match your interests and connect with like-minded people
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => invalidate()}
          disabled={isInvalidating}
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${isInvalidating ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </Button>
      </div>

      {/* Personalized Recommendations */}
      <RecommendationCard
        title="Recommended for You"
        description="Communities tailored to your interests and activity preferences"
        icon={<Sparkles className="w-6 h-6 text-blue-400" />}
        type="personalized"
        limit={8}
      />

      {/* Trending Hubs */}
      <RecommendationCard
        title="Trending Now"
        description="Popular hubs with growing communities and active discussions"
        icon={<TrendingUp className="w-6 h-6 text-green-400" />}
        type="trending"
        limit={6}
      />

      {/* Most Active Today */}
      <RecommendationCard
        title="Most Active Today"
        description="Hubs with the highest activity and engagement right now"
        icon={<Activity className="w-6 h-6 text-orange-400" />}
        type="activity"
        limit={6}
      />
    </div>
  );
}

/**
 * Individual recommendation section card
 */
function RecommendationCard({
  title,
  description,
  icon,
  type,
  limit,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  type: 'personalized' | 'trending' | 'activity' | 'similar' | 'friends';
  limit: number;
}) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-3">
          {icon}
          <div>
            <CardTitle className="text-white text-xl">{title}</CardTitle>
            <CardDescription className="text-gray-400 mt-1">
              {description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Recommendations
          type={type}
          limit={limit}
          showHeader={false}
          className=""
        />
      </CardContent>
    </Card>
  );
}

/**
 * Compact recommendations widget for sidebar or smaller spaces
 */
export function CompactRecommendations({
  type = 'personalized',
  limit = 4,
}: {
  type?: 'personalized' | 'trending' | 'activity';
  limit?: number;
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white flex items-center">
        <Sparkles className="w-5 h-5 mr-2 text-blue-400" />
        Quick Picks
      </h3>
      <Recommendations
        type={type}
        limit={limit}
        showHeader={false}
        className="grid grid-cols-1 gap-3"
      />
    </div>
  );
}

/**
 * Recommendations banner for the top of the page
 */
export function RecommendationsBanner() {
  return (
    <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-lg p-6 mb-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-white mb-2 flex items-center">
            <Sparkles className="w-6 h-6 mr-2 text-blue-400" />
            Discover Your Perfect Community
          </h3>
          <p className="text-gray-300">
            Get personalized hub recommendations based on your interests and activity
          </p>
        </div>
        <div className="hidden md:block">
          <CompactRecommendations type="personalized" limit={3} />
        </div>
      </div>
    </div>
  );
}

/**
 * Empty state component when no recommendations are available
 */
export function EmptyRecommendations() {
  return (
    <Card className="bg-gray-800/50 border-gray-700 text-center py-12">
      <CardContent>
        <Sparkles className="w-16 h-16 text-gray-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">
          No Recommendations Yet
        </h3>
        <p className="text-gray-400 mb-6 max-w-md mx-auto">
          Start by joining some hubs or setting your interests to get personalized recommendations
        </p>
        <div className="space-y-3">
          <Button variant="default" className="mr-3">
            Browse All Hubs
          </Button>
          <Button variant="outline">
            Set Your Interests
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Recommendations loading state
 */
export function RecommendationsLoading() {
  return (
    <div className="space-y-8">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-gray-600 rounded animate-pulse" />
              <div>
                <div className="w-48 h-6 bg-gray-600 rounded animate-pulse mb-2" />
                <div className="w-64 h-4 bg-gray-700 rounded animate-pulse" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, j) => (
                <div key={j} className="bg-gray-700 rounded-lg p-4 animate-pulse">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-12 h-12 bg-gray-600 rounded-lg" />
                    <div className="flex-1">
                      <div className="w-24 h-4 bg-gray-600 rounded mb-2" />
                      <div className="w-16 h-3 bg-gray-700 rounded" />
                    </div>
                  </div>
                  <div className="space-y-2 mb-4">
                    <div className="w-full h-3 bg-gray-600 rounded" />
                    <div className="w-3/4 h-3 bg-gray-600 rounded" />
                  </div>
                  <div className="w-full h-8 bg-gray-600 rounded" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
