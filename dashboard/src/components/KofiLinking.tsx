"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Link, Unlink, Crown, Calendar, DollarSign } from "lucide-react";

interface KofiStatus {
  isLinked: boolean;
  email?: string;
  isVerified?: boolean;
  hasMediaPremium: boolean;
  mediaPremiumExpiresAt?: string;
  recentDonations: Array<{
    id: string;
    amount: number;
    currency: string;
    tierName?: string;
    isSubscription: boolean;
    kofiTimestamp: string;
    processed: boolean;
  }>;
}

export function KofiLinking() {
  const [status, setStatus] = useState<KofiStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const { toast } = useToast();

  // Fetch current Ko-fi status
  const fetchStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/user/link-kofi");
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch Ko-fi status",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching Ko-fi status:", error);
      toast({
        title: "Error",
        description: "Failed to fetch Ko-fi status",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Link Ko-fi account using verified Discord email
  const linkAccount = async () => {
    try {
      setIsLinking(true);
      const response = await fetch("/api/user/link-kofi", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: data.premiumGranted
            ? "Ko-fi account linked with verified Discord email and premium access granted!"
            : "Ko-fi account linked successfully using verified Discord email!",
        });
        await fetchStatus(); // Refresh status
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to verify Discord email for Ko-fi linking",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error linking Ko-fi account:", error);
      toast({
        title: "Error",
        description: "Failed to link Ko-fi account",
        variant: "destructive",
      });
    } finally {
      setIsLinking(false);
    }
  };

  // Unlink Ko-fi account
  const unlinkAccount = async () => {
    try {
      setIsLinking(true);
      const response = await fetch("/api/user/link-kofi", {
        method: "DELETE",
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Ko-fi account unlinked successfully",
        });
        await fetchStatus(); // Refresh status
      } else {
        toast({
          title: "Error",
          description: "Failed to unlink Ko-fi account",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error unlinking Ko-fi account:", error);
      toast({
        title: "Error",
        description: "Failed to unlink Ko-fi account",
        variant: "destructive",
      });
    } finally {
      setIsLinking(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, [fetchStatus]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Ko-fi Account Linking
          </CardTitle>
          <CardDescription>
            Link your Ko-fi account to automatically receive premium benefits when you support InterChat
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {status?.isLinked ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                <div>
                  <p className="font-medium text-green-800 dark:text-green-200">
                    Linked to: {status.email}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    ✅ Verified Discord email - Ko-fi donations will automatically grant premium benefits
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={unlinkAccount}
                  disabled={isLinking}
                >
                  {isLinking ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Unlink className="h-4 w-4" />
                  )}
                  Unlink
                </Button>
              </div>

              {/* Premium Status */}
              {status.hasMediaPremium && (
                <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Crown className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="font-medium text-yellow-800 dark:text-yellow-200">
                        Premium Active
                      </p>
                      <p className="text-sm text-yellow-600 dark:text-yellow-400">
                        {status.mediaPremiumExpiresAt
                          ? `Expires: ${new Date(status.mediaPremiumExpiresAt).toLocaleDateString()}`
                          : "Permanent premium access"
                        }
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  🔒 Secure Discord Email Verification
                </h4>
                <p className="text-sm text-blue-600 dark:text-blue-400 mb-3">
                  We&apos;ll use your verified Discord email address to securely link your Ko-fi donations.
                  This prevents fraud and ensures only you can claim premium benefits.
                </p>
                <ul className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                  <li>• No manual email input required</li>
                  <li>• Uses your verified Discord account email</li>
                  <li>• Prevents unauthorized access to premium benefits</li>
                  <li>• Automatically processes existing Ko-fi donations</li>
                </ul>
              </div>
              <Button onClick={linkAccount} disabled={isLinking} className="w-full">
                {isLinking ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Link className="h-4 w-4 mr-2" />
                )}
                Link Ko-fi Account with Discord Email
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Donations */}
      {status?.recentDonations && status.recentDonations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Recent Ko-fi Donations
            </CardTitle>
            <CardDescription>
              Your recent Ko-fi donations linked to this account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {status.recentDonations.map((donation) => (
                <div
                  key={donation.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-medium">
                        {donation.amount} {donation.currency}
                      </span>
                    </div>
                    {donation.tierName && (
                      <Badge variant="outline">
                        {donation.tierName}
                      </Badge>
                    )}
                    {donation.isSubscription && (
                      <Badge variant="secondary">
                        <Calendar className="h-3 w-3 mr-1" />
                        Subscription
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">
                      {new Date(donation.kofiTimestamp).toLocaleDateString()}
                    </p>
                    <Badge
                      variant={donation.processed ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {donation.processed ? "Processed" : "Pending"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
