"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import {
  Loader2,
  Link,
  Unlink,
  Crown,
  Calendar,
  DollarSign,
  Shield,
  CheckCircle,
  Coffee,
  Sparkles,
  ArrowRight,
  Heart
} from "lucide-react";

interface KofiStatus {
  isLinked: boolean;
  email?: string;
  isVerified?: boolean;
  hasMediaPremium: boolean;
  mediaPremiumExpiresAt?: string;
  recentDonations: Array<{
    id: string;
    amount: number;
    currency: string;
    tierName?: string;
    isSubscription: boolean;
    kofiTimestamp: string;
    processed: boolean;
  }>;
}

export function KofiLinking() {
  const [status, setStatus] = useState<KofiStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const { toast } = useToast();

  // Fetch current Ko-fi status
  const fetchStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/user/link-kofi");
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch Ko-fi status",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching Ko-fi status:", error);
      toast({
        title: "Error",
        description: "Failed to fetch Ko-fi status",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Link Ko-fi account using verified Discord email
  const linkAccount = async () => {
    try {
      setIsLinking(true);
      const response = await fetch("/api/user/link-kofi", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: data.premiumGranted
            ? "Ko-fi account linked with verified Discord email and premium access granted!"
            : "Ko-fi account linked successfully using verified Discord email!",
        });
        await fetchStatus(); // Refresh status
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to verify Discord email for Ko-fi linking",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error linking Ko-fi account:", error);
      toast({
        title: "Error",
        description: "Failed to link Ko-fi account",
        variant: "destructive",
      });
    } finally {
      setIsLinking(false);
    }
  };

  // Unlink Ko-fi account
  const unlinkAccount = async () => {
    try {
      setIsLinking(true);
      const response = await fetch("/api/user/link-kofi", {
        method: "DELETE",
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Ko-fi account unlinked successfully",
        });
        await fetchStatus(); // Refresh status
      } else {
        toast({
          title: "Error",
          description: "Failed to unlink Ko-fi account",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error unlinking Ko-fi account:", error);
      toast({
        title: "Error",
        description: "Failed to unlink Ko-fi account",
        variant: "destructive",
      });
    } finally {
      setIsLinking(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, [fetchStatus]);

  if (isLoading) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
        <CardContent className="flex flex-col items-center justify-center p-12">
          <div className="relative">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <div className="absolute inset-0 h-8 w-8 animate-ping rounded-full bg-blue-600/20"></div>
          </div>
          <p className="mt-4 text-sm text-muted-foreground">Loading Ko-fi status...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 overflow-hidden">
        <div className="relative">
          {/* Decorative background pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-pink-600/5"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-500/10 to-orange-500/10 rounded-full blur-2xl"></div>

          <CardHeader className="relative pb-8">
            <div className="flex items-center gap-3 mb-2">
              <div className="relative">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Coffee className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 p-1 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full">
                  <Heart className="h-3 w-3 text-white" />
                </div>
              </div>
              <div>
                <CardTitle className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  Ko-fi Account Linking
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Shield className="h-4 w-4 text-green-600" />
                </div>
              </div>
            </div>
            <CardDescription className="text-base text-gray-600 dark:text-gray-400 leading-relaxed">
              Connect your Ko-fi account to automatically unlock premium features when you support InterChat.
              <span className="font-medium text-blue-600 dark:text-blue-400"> Secure Discord verification ensures your benefits are protected.</span>
            </CardDescription>
          </CardHeader>
        </div>
        <CardContent className="relative space-y-6">
          {status?.isLinked ? (
            <div className="space-y-6">
              {/* Successfully Linked Status */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 border border-green-200/50 dark:border-green-800/50">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5"></div>
                <div className="relative p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg">
                        <CheckCircle className="h-6 w-6 text-white" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-bold text-green-800 dark:text-green-200 text-lg">
                            Successfully Linked
                          </h3>
                          <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800">
                            <Shield className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        </div>
                        <p className="font-medium text-green-700 dark:text-green-300">
                          {status.email}
                        </p>
                        <p className="text-sm text-green-600 dark:text-green-400 leading-relaxed">
                          Your Ko-fi donations will automatically unlock premium features.
                          Discord email verification ensures secure benefit delivery.
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={unlinkAccount}
                      disabled={isLinking}
                      className="border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-300 dark:hover:bg-green-950"
                    >
                      {isLinking ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Unlink className="h-4 w-4" />
                      )}
                      Unlink
                    </Button>
                  </div>
                </div>
              </div>

              {/* Premium Status */}
              {status.hasMediaPremium && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 dark:from-amber-950/50 dark:via-yellow-950/50 dark:to-orange-950/50 border border-amber-200/50 dark:border-amber-800/50">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 via-yellow-500/5 to-orange-500/5"></div>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-2xl"></div>
                  <div className="relative p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <div className="p-3 bg-gradient-to-br from-amber-500 via-yellow-500 to-orange-500 rounded-xl shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1">
                            <Sparkles className="h-4 w-4 text-yellow-500 animate-pulse" />
                          </div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-bold text-amber-800 dark:text-amber-200 text-lg">
                              Premium Active
                            </h3>
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-green-600 dark:text-green-400">LIVE</span>
                            </div>
                          </div>
                          <p className="text-sm text-amber-600 dark:text-amber-400 font-medium">
                            {status.mediaPremiumExpiresAt
                              ? `Expires: ${new Date(status.mediaPremiumExpiresAt).toLocaleDateString()}`
                              : "Permanent premium access"
                            }
                          </p>
                          <p className="text-xs text-amber-600/80 dark:text-amber-400/80">
                            Unlimited media sharing • Priority support • Exclusive features
                          </p>
                        </div>
                      </div>
                      <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg">
                        <Crown className="h-3 w-3 mr-1" />
                        Premium
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {/* Link Button */}
              <div className="relative">
                <Button
                  onClick={linkAccount}
                  disabled={isLinking}
                  className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] disabled:transform-none"
                >
                  {isLinking ? (
                    <div className="flex items-center gap-3">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span>Verifying Discord Email...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-white/20 rounded-lg">
                        <Link className="h-5 w-5" />
                      </div>
                      <span>Link Ko-fi Account with Discord Email</span>
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </div>
                  )}
                </Button>
                {!isLinking && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 rounded-lg blur-xl -z-10 animate-pulse"></div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Donations */}
      {status?.recentDonations && status.recentDonations.length > 0 && (
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 overflow-hidden">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-blue-600/5 to-purple-600/5"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-full blur-2xl"></div>

            <CardHeader className="relative pb-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl shadow-lg">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                    Recent Ko-fi Donations
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your recent Ko-fi donations linked to this account
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </div>

          <CardContent className="relative">
            <div className="space-y-4">
              {status.recentDonations.map((donation) => (
                <div
                  key={donation.id}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 border border-gray-200/50 dark:border-gray-700/50 p-4 hover:shadow-lg transition-all duration-300 hover:scale-[1.01]"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <div className="relative flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-md">
                        <Coffee className="h-5 w-5 text-white" />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <span className="font-bold text-lg text-green-600 dark:text-green-400">
                              ${donation.amount}
                            </span>
                            <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                              {donation.currency}
                            </span>
                          </div>

                          {donation.tierName && (
                            <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                              <Crown className="h-3 w-3 mr-1" />
                              {donation.tierName}
                            </Badge>
                          )}

                          {donation.isSubscription && (
                            <Badge variant="secondary" className="bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                              <Calendar className="h-3 w-3 mr-1" />
                              Subscription
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {new Date(donation.kofiTimestamp).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>

                    <div className="text-right space-y-2">
                      <Badge
                        variant={donation.processed ? "default" : "secondary"}
                        className={donation.processed
                          ? "bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800"
                          : "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-800"
                        }
                      >
                        {donation.processed ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Processed
                          </>
                        ) : (
                          <>
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Pending
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
