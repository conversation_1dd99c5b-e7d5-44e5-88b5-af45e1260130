"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import type React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { memo } from "react";

const FooterSection = memo(
  ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="w-full sm:w-1/2 md:w-1/5 mb-8 md:mb-0">
      <h4 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100">
        {title}
      </h4>
      {children}
    </div>
  )
);
FooterSection.displayName = "FooterSection";

const FooterLink = memo(
  ({
    href,
    external,
    children,
  }: {
    href: string;
    external?: boolean;
    children: React.ReactNode;
  }) => (
    <li>
      <Link
        href={href}
        target={external ? "_blank" : undefined}
        rel={external ? "noreferrer" : undefined}
        className="text-gray-600 dark:text-gray-400 hover:text-primary transition-colors duration-200 text-sm flex items-center gap-2"
      >
        {children}
      </Link>
    </li>
  )
);
FooterLink.displayName = "FooterLink";

export default function Footer() {
  return (
    <footer className="relative border-t dark:border-zinc-800 border-zinc-200">
      <div className="dark:bg-black/40 bg-white/40 backdrop-blur-xl">
        <div className="container mx-auto px-6 py-12">
          <div className="flex flex-wrap justify-between">
            {/* Brand Section */}
            <div className="w-full md:w-1/4 mb-8 md:mb-0">
              <div className="flex items-center mb-4">
                <Image
                  src="/interchat.png"
                  alt="InterChat Logo"
                  width={32}
                  height={32}
                  className="mr-2"
                  loading="lazy"
                />
                <h3 className="text-2xl font-bold text-primary">InterChat</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Connecting Discord communities, one message at a time.
              </p>
              <div className="flex space-x-4 mb-6">
                <Button
                  variant="outline"
                  size="icon"
                  asChild
                  className="rounded-full dark:bg-accent bg-accent-foreground"
                >
                  <Link href="https://github.com/interchatapp/InterChat">
                    <Github className="h-4 w-4" />
                    <span className="sr-only">GitHub</span>
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  asChild
                  className="rounded-full dark:bg-accent bg-accent-foreground"
                >
                  <Link href="https://x.com/InterChatApp">
                    <Twitter className="h-4 w-4" />
                    <span className="sr-only">Twitter</span>
                  </Link>
                </Button>
              </div>
              <Button
                className="bg-[#FF5E5B] hover:bg-[#FF5E5B]/90 text-white"
                asChild
              >
                <Link href="/donate" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  <span>Support Us</span>
                </Link>
              </Button>
            </div>

            {/* Quick Links */}
            <FooterSection title="Product">
              <ul className="space-y-2">
                <FooterLink href="/">Home</FooterLink>
                <FooterLink href="/docs">Documentation</FooterLink>
                <FooterLink href="/hubs">Hubs</FooterLink>
                <FooterLink href="/dashboard" external>
                  Dashboard
                </FooterLink>
              </ul>
            </FooterSection>

            {/* Community */}
            <FooterSection title="Community">
              <ul className="space-y-2">
                <FooterLink href="/support" external>
                  Discord Server
                </FooterLink>
                <FooterLink href="/invite" external>
                  Add to Discord
                </FooterLink>

                <FooterLink href="/vote" external>
                  Vote for Us
                </FooterLink>
                <FooterLink
                  href="https://github.com/interchatapp/InterChat"
                  external
                >
                  GitHub
                </FooterLink>
              </ul>
            </FooterSection>

            {/* Legal */}
            <FooterSection title="Legal">
              <ul className="space-y-2">
                <FooterLink href="/terms">Terms of Service</FooterLink>
                <FooterLink href="/privacy">Privacy Policy</FooterLink>
                <FooterLink href="/guidelines">Community Guidelines</FooterLink>
              </ul>
            </FooterSection>
          </div>

          {/* Bottom Bar */}
          <div className="mt-12 pt-8 border-t border-zinc-200 dark:border-zinc-800">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                © {new Date().getFullYear()} InterChat. All rights reserved.
              </p>
              <div className="flex items-center mt-4 md:mt-0">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Made with
                </span>
                <Heart className="h-4 w-4 mx-1 text-red-500 fill-red-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  by the InterChat Team
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
