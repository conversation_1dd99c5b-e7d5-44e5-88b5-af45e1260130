"use client";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";

interface UnderlinedTabsProps {
  defaultValue: string;
  tabs: {
    value: string;
    label: React.ReactNode;
    color?: "indigo" | "blue" | "green" | "purple" | "red" | "pink" | "orange";
    icon?: React.ReactNode;
    href?: string;
  }[];
  children?: React.ReactNode;
  className?: string;
  navigational?: boolean;
}

export function UnderlinedTabs({
  defaultValue,
  tabs,
  children,
  className,
  navigational = false,
}: UnderlinedTabsProps) {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(true);

  // Check scroll position to show/hide indicators
  const checkScroll = () => {
    if (!tabsContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = tabsContainerRef.current;
    setShowLeftIndicator(scrollLeft > 0);
    setShowRightIndicator(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
  };

  // Add scroll event listener
  useEffect(() => {
    const tabsContainer = tabsContainerRef.current;
    if (tabsContainer) {
      tabsContainer.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();

      // Check on window resize too
      window.addEventListener('resize', checkScroll);

      // Add a subtle scroll hint animation for mobile
      if (window.innerWidth < 640 && tabsContainer.scrollWidth > tabsContainer.clientWidth) {
        // Briefly scroll right and back to show users it's scrollable
        setTimeout(() => {
          tabsContainer.scrollTo({ left: 40, behavior: 'smooth' });
          setTimeout(() => {
            tabsContainer.scrollTo({ left: 0, behavior: 'smooth' });
          }, 800);
        }, 1000);
      }

      return () => {
        tabsContainer.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      };
    }
  }, []);
  const colorMap: Record<string, { borderColor: string; color: string }> = {
    indigo: { borderColor: "#6366f1", color: "#818cf8" },
    blue: { borderColor: "#3b82f6", color: "#60a5fa" },
    green: { borderColor: "#10b981", color: "#34d399" },
    purple: { borderColor: "#8b5cf6", color: "#a78bfa" },
    red: { borderColor: "#ef4444", color: "#f87171" },
    pink: { borderColor: "#ec4899", color: "#f472b6" },
    orange: { borderColor: "#f59e0b", color: "#fbbf24" },
    default: { borderColor: "#6366f1", color: "#818cf8" },
  };

  return (
    <Tabs
      defaultValue={defaultValue}
      className={`w-full space-y-6 ${className || ""}`}
    >
      <div
        ref={tabsContainerRef}
        className="relative overflow-x-auto -mx-6 px-0 border-b border-gray-800/50 bg-gray-900/80 backdrop-blur-md z-10 shadow-sm transition-all duration-200 no-scrollbar"
      >
        <div className="px-4 sm:px-6 w-full">
          {/* Scroll indicators for mobile */}
          {showRightIndicator && (
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-900/80 to-transparent pointer-events-none sm:hidden"></div>
          )}
          {showLeftIndicator && (
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-900/80 to-transparent pointer-events-none sm:hidden"></div>
          )}
          <TabsList className="w-full flex flex-nowrap justify-start sm:justify-center gap-2 sm:gap-6 bg-transparent max-w-screen-xl mx-auto h-auto p-0 rounded-none">
            {tabs.map((tab) => {
              const color = tab.color || "indigo";
              const getColorStyles = (state: string) => {
                if (state !== "active")
                  return { borderColor: "transparent", color: "" };

                return colorMap[color] || colorMap.indigo;
              };

              // For navigational tabs, wrap in Link
              const tabContent = (
                <div className="flex items-center justify-center">
                  {tab.icon && <span className="mr-1 sm:mr-2">{tab.icon}</span>}
                  <span className="text-xs sm:text-sm">{tab.label}</span>
                </div>
              );

              const component = (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className="cursor-pointer px-3 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-400 border-b-[2px] border-transparent data-[state=active]:border-[var(--tab-border-color)] data-[state=active]:text-[var(--tab-text-color)] transition-all duration-200 hover:text-gray-300 bg-transparent rounded-none shadow-none focus:outline-none focus:ring-0 focus:shadow-none whitespace-nowrap"
                  style={
                    {
                      "--tab-border-color":
                        getColorStyles("active").borderColor,
                      "--tab-text-color": getColorStyles("active").color,
                    } as React.CSSProperties
                  }
                >
                  {tabContent}
                </TabsTrigger>
              );

              return navigational && tab.href ? (
                <Link href={tab.href} key={tab.value}>
                  {component}
                </Link>
              ) : (
                component
              );
            })}
          </TabsList>
        </div>
      </div>
      {!navigational && children}
    </Tabs>
  );
}
