"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import {
  Activity,
  Calendar,
  Hash,
  Link as LinkIcon,
  Palette,
  Zap,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff,
  Shield,
  Globe,
  Server
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface ConnectionOverviewProps {
  connection: Connection & { hub: Hub; server: ServerData };
}

export function ConnectionOverview({ connection }: ConnectionOverviewProps) {
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true });
  };

  return (
    <div className="space-y-6">
      {/* Comprehensive Connection Overview */}
      <Card className="border border-gray-800/50 bg-gradient-to-r from-gray-900/80 to-gray-950/80 backdrop-blur-sm relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5" />
        <CardHeader className="relative px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <Activity className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl">Connection Overview</CardTitle>
                <CardDescription className="text-gray-400">
                  Complete status and configuration summary
                </CardDescription>
              </div>
            </div>
            <Badge
              variant={connection.connected ? "default" : "secondary"}
              className={`${connection.connected
                ? "bg-green-500/10 text-green-400 border-green-500/20"
                : "bg-gray-500/10 text-gray-400 border-gray-500/20"
              } text-sm px-3 py-1`}
            >
              {connection.connected ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Active Connection
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Inactive Connection
                </>
              )}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          {/* Key Information Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Hub Information */}
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-md bg-purple-500/10 border border-purple-500/20">
                  <Globe className="h-4 w-4 text-purple-400" />
                </div>
                <div>
                  <div className="font-medium text-white">Connected Hub</div>
                  <div className="text-sm text-gray-400">Community hub details</div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-white">{connection.hub.name}</span>
                  <Badge
                    variant="outline"
                    className={`text-xs ${connection.hub.private
                      ? "border-purple-500/30 text-purple-400 bg-purple-500/10"
                      : "border-green-500/30 text-green-400 bg-green-500/10"
                    }`}
                  >
                    {connection.hub.private ? "Private" : "Public"}
                  </Badge>
                </div>
                <p className="text-sm text-gray-400 line-clamp-2">{connection.hub.description}</p>
              </div>
            </div>

            {/* Server Information */}
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-md bg-indigo-500/10 border border-indigo-500/20">
                  <Server className="h-4 w-4 text-indigo-400" />
                </div>
                <div>
                  <div className="font-medium text-white">Discord Server</div>
                  <div className="text-sm text-gray-400">Server and channel info</div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="font-medium text-white">{connection.server.name}</div>
                <div className="flex items-center gap-2 text-sm">
                  <Hash className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-300 font-mono text-xs">{connection.channelId}</span>
                  <Badge variant="outline" className="text-xs border-gray-600 text-gray-400 px-1 py-0">
                    Channel
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Settings Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
            {/* Connection Status */}
            <div className="p-3 rounded-lg border border-gray-800/50 bg-gray-900/20 text-center">
              <div className="flex items-center justify-center mb-2">
                <Zap className={`h-4 w-4 ${connection.connected ? 'text-green-400' : 'text-red-400'}`} />
              </div>
              <div className="text-xs text-gray-400 mb-1">Status</div>
              <div className={`text-sm font-medium ${connection.connected ? 'text-green-400' : 'text-red-400'}`}>
                {connection.connected ? 'Connected' : 'Disconnected'}
              </div>
            </div>

            {/* Display Mode */}
            <div className="p-3 rounded-lg border border-gray-800/50 bg-gray-900/20 text-center">
              <div className="flex items-center justify-center mb-2">
                {connection.compact ? (
                  <Eye className="h-4 w-4 text-blue-400" />
                ) : (
                  <EyeOff className="h-4 w-4 text-blue-400" />
                )}
              </div>
              <div className="text-xs text-gray-400 mb-1">Display</div>
              <div className="text-sm font-medium text-blue-400">
                {connection.compact ? 'Compact' : 'Full'}
              </div>
            </div>

            {/* Join Requests */}
            <div className="p-3 rounded-lg border border-gray-800/50 bg-gray-900/20 text-center">
              <div className="flex items-center justify-center mb-2">
                <Shield className="h-4 w-4 text-orange-400" />
              </div>
              <div className="text-xs text-gray-400 mb-1">Join Requests</div>
            </div>

            {/* Last Active */}
            <div className="p-3 rounded-lg border border-gray-800/50 bg-gray-900/20 text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="h-4 w-4 text-indigo-400" />
              </div>
              <div className="text-xs text-gray-400 mb-1">Last Active</div>
              <div className="text-sm font-medium text-indigo-400">
                {formatDate(connection.lastActive)}
              </div>
            </div>
          </div>

          {/* Embed Color (if set) */}
          {connection.embedColor && (
            <div className="mt-4 p-3 rounded-lg border border-gray-800/50 bg-gray-900/20">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-purple-500/10 border border-purple-500/20">
                  <Palette className="h-4 w-4 text-purple-400" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-white">Custom Embed Color</div>
                  <div className="text-sm text-gray-400">Message accent color</div>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded border border-gray-600"
                    style={{ backgroundColor: connection.embedColor }}
                  />
                  <span className="text-sm font-mono text-gray-300">{connection.embedColor}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Details */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="px-6 py-4">
          <CardTitle className="text-lg">Additional Details</CardTitle>
          <CardDescription className="text-gray-400">
            Technical information and metadata
          </CardDescription>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Server Invite Link (if exists) */}
            {connection.invite && (
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 rounded-md bg-green-500/10 border border-green-500/20">
                    <LinkIcon className="h-4 w-4 text-green-400" />
                  </div>
                  <div>
                    <div className="font-medium text-white">Server Invite</div>
                    <div className="text-sm text-gray-400">Active invitation link</div>
                  </div>
                </div>
                <div className="text-xs font-mono bg-gray-800/50 px-3 py-2 rounded border border-gray-700 break-all">
                  {connection.invite}
                </div>
              </div>
            )}

            {/* Connection ID */}
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-md bg-gray-500/10 border border-gray-500/20">
                  <Hash className="h-4 w-4 text-gray-400" />
                </div>
                <div>
                  <div className="font-medium text-white">Connection ID</div>
                  <div className="text-sm text-gray-400">Unique identifier</div>
                </div>
              </div>
              <div className="text-xs font-mono bg-gray-800/50 px-3 py-2 rounded border border-gray-700 break-all">
                {connection.id}
              </div>
            </div>

            {/* Parent Channel (if thread) */}
            {connection.parentId && (
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 rounded-md bg-yellow-500/10 border border-yellow-500/20">
                    <Hash className="h-4 w-4 text-yellow-400" />
                  </div>
                  <div>
                    <div className="font-medium text-white">Parent Channel</div>
                    <div className="text-sm text-gray-400">Thread parent ID</div>
                  </div>
                </div>
                <div className="text-xs font-mono bg-gray-800/50 px-3 py-2 rounded border border-gray-700 break-all">
                  {connection.parentId}
                </div>
              </div>
            )}

            {/* Creation Date */}
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-md bg-blue-500/10 border border-blue-500/20">
                  <Calendar className="h-4 w-4 text-blue-400" />
                </div>
                <div>
                  <div className="font-medium text-white">Created</div>
                  <div className="text-sm text-gray-400">Connection established</div>
                </div>
              </div>
              <div className="text-sm text-gray-300">
                {formatDate(connection.createdAt)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
