"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit3, X, Check } from "lucide-react";
import { PremiumBadge, PremiumIndicator } from "@/components/ui/premium-badge";
import { InlineUpgradePrompt } from "@/components/ui/upgrade-prompt";

interface HubNameCustomizationProps {
  hubId: string;
  hubName: string;
  hasPremium: boolean;
  isOwner: boolean;
  onNameUpdate?: (newName: string) => void;
}

export function HubNameCustomization({
  hubId,
  hubName,
  hasPremium,
  isOwner,
  onNameUpdate,
}: HubNameCustomizationProps) {
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(hubName);
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    // Check if user has permission
    if (!isOwner) {
      toast({
        title: "Permission Denied",
        description: "Only hub owners can change the hub name",
        variant: "destructive",
      });
      return;
    }

    if (!hasPremium) {
      toast({
        title: "Premium Feature Required",
        description: "Hub name customization is available to Ko-fi Supporters ($2.99/month)",
        variant: "destructive",
      });
      return;
    }

    if (!newName.trim()) {
      toast({
        title: "Invalid Name",
        description: "Hub name cannot be empty",
        variant: "destructive",
      });
      return;
    }

    if (newName.trim() === hubName) {
      toast({
        title: "No Changes",
        description: "The new name is the same as the current name",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/hubs/${hubId}/customize-name`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newName.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || "Failed to update hub name");
      }

      toast({
        title: "Hub Name Updated",
        description: `Your hub has been renamed to "${newName.trim()}"`,
      });

      onNameUpdate?.(newName.trim());
      setIsEditing(false);
    } catch (error) {
      console.error("Error updating hub name:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update hub name",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setNewName(hubName);
    setIsEditing(false);
  };

  // Show different states based on permissions and premium status
  if (!isOwner) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Hub Name Customization
            <PremiumBadge variant="required" size="sm" />
          </CardTitle>
          <CardDescription>
            Only hub owners can customize hub names
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="hub-name">Hub Name</Label>
            <Input
              value={hubName}
              disabled
              className="bg-gray-800/50 border-gray-700/50 text-gray-300"
            />
            <p className="text-xs text-gray-400">
              You need to be the hub owner to change the name
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasPremium) {
    return (
      <Card className="border border-yellow-500/30 bg-gradient-to-b from-yellow-900/20 to-amber-900/20 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Hub Name Customization
            <PremiumBadge variant="gold" size="sm" animated />
          </CardTitle>
          <CardDescription>
            Unlock hub name customization with Ko-fi Supporter membership
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="hub-name">Hub Name</Label>
            <Input
              value={hubName}
              disabled
              className="bg-gray-800/50 border-gray-700/50 text-gray-300"
            />
            <p className="text-xs text-gray-400">
              Premium feature - upgrade to customize your hub name
            </p>
          </div>

          <InlineUpgradePrompt
            feature="Hub name customization"
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border border-yellow-500/30 bg-gradient-to-b from-yellow-900/10 via-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-lg shadow-yellow-500/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Hub Name Customization
          <PremiumBadge variant="sparkly" size="sm" animated />
        </CardTitle>
        <CardDescription>
          Change your hub name with premium flexibility and special characters
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="hub-name" className="flex items-center gap-2">
            Hub Name
          </Label>

          {isEditing ? (
            <div className="flex gap-2">
              <Input
                id="hub-name"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="Enter new hub name"
                maxLength={50}
                className="bg-gray-900/50 border-gray-700/50 focus:border-yellow-500/50 focus:ring-yellow-500/20"
              />
              <Button
                onClick={handleSave}
                disabled={isLoading || !newName.trim() || newName.trim() === hubName}
                size="sm"
                className="bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 border-none text-black font-medium"
              >
                <Check className="w-4 h-4" />
              </Button>
              <Button
                onClick={handleCancel}
                disabled={isLoading}
                size="sm"
                variant="outline"
                className="border-yellow-700/50 hover:bg-yellow-800/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex gap-2">
              <Input
                value={hubName}
                disabled
                className="bg-gray-800/50 border-gray-700/50 text-gray-300"
              />
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="border-yellow-700/50 hover:bg-yellow-800/20 text-yellow-400 hover:text-yellow-300"
              >
                <Edit3 className="w-4 h-4" />
              </Button>
            </div>
          )}

          <p className="text-xs text-gray-400">
            {newName.length}/50 characters
          </p>
        </div>

        <div className="p-3 rounded-lg bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border border-yellow-500/20">
          <p className="text-sm text-yellow-400">
            <strong>✨ Premium Feature:</strong> You can use special characters, emojis, and longer names (up to 50 characters) that regular users cannot.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
