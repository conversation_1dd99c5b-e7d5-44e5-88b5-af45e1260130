/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import Context from '#src/core/CommandContext/Context.js';
import MediaUsageService from '#src/services/MediaUsageService.js';
import { CallDatabaseService } from '#src/services/CallDatabaseService.js';
import Constants from '#src/utils/Constants.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { stripIndents } from 'common-tags';

export default class GifCommand extends BaseCommand {
  private readonly mediaUsageService = new MediaUsageService();
  private readonly callDbService = new CallDatabaseService();
  readonly cooldown = 12_000; // 12 second cooldown

  constructor() {
    super({
      name: 'gif',
      description: '🎬 Share GIFs during calls (alias for media)',
      types: { prefix: true },
      contexts: { guildOnly: true },
    });
  }

  async execute(ctx: Context): Promise<void> {
    const args = ctx.args;

    if (!args.length) {
      await this.showGifHelp(ctx);
      return;
    }

    // Treat all arguments as GIF URL
    await this.shareGif(ctx, args.join(' '));
  }

  private async showGifHelp(ctx: Context): Promise<void> {
    const embed = new EmbedBuilder()
      .setColor(Constants.Colors.invisible)
      .setTitle('🎬 GIF Sharing')
      .setDescription(
        stripIndents`
          Share GIFs during your InterChat calls!

          **Usage:**
          \`.gif <url>\` - Share a GIF URL

          **Supported GIF Sources:**
          • **Tenor**: \`https://tenor.com/view/...\`
          • **Giphy**: \`https://giphy.com/gifs/...\`
          • **Direct GIF URLs**: \`https://example.com/image.gif\`

          **Limits:**
          • **2 free** GIFs per call
          • **+5 more** after voting on Top.gg
          • **Unlimited** with premium ($3/month)
        `,
      )
      .setFooter({
        text: 'You must be in an active call to share GIFs',
        iconURL: ctx.client.user.displayAvatarURL(),
      });

    const buttons = new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setStyle(ButtonStyle.Link)
        .setLabel('Vote for More')
        .setEmoji('🗳️')
        .setURL(Constants.Links.Vote),
      new ButtonBuilder()
        .setStyle(ButtonStyle.Link)
        .setLabel('Get Premium')
        .setEmoji('⭐')
        .setURL(Constants.Links.Donate),
    );

    await ctx.reply({ embeds: [embed], components: [buttons] });
  }

  private async shareGif(ctx: Context, gifUrl: string): Promise<void> {
    // Check if user is in an active call
    const activeCall = await this.callDbService.getActiveCallByChannelId(ctx.channelId);
    if (!activeCall) {
      const embed = new EmbedBuilder()
        .setColor(Constants.Colors.invisible)
        .setDescription(`${ctx.getEmoji('x_icon')} You must be in an active call to share GIFs.`)
        .setFooter({
          text: 'Use /call to start a call',
          iconURL: ctx.client.user.displayAvatarURL(),
        });

      await ctx.reply({ embeds: [embed] });
      return;
    }

    // Validate GIF URL
    if (!this.isValidGifUrl(gifUrl)) {
      const embed = new EmbedBuilder()
        .setColor(Constants.Colors.invisible)
        .setDescription(
          stripIndents`
            ${ctx.getEmoji('x_icon')} Invalid GIF URL provided.

            **Supported GIF formats:**
            • Tenor: \`https://tenor.com/view/example\`
            • Giphy: \`https://giphy.com/gifs/example\`
            • Direct GIF: \`https://example.com/image.gif\`
          `,
        );

      await ctx.reply({ embeds: [embed] });
      return;
    }

    // Attempt to use media
    const result = await this.mediaUsageService.useMedia(
      activeCall.id,
      ctx.user.id,
      'GIF',
      gifUrl,
    );

    if (result.success) {
      const embed = new EmbedBuilder()
        .setColor('#00FF00')
        .setDescription(`${ctx.getEmoji('tick_icon')} ${result.message}`);

      await ctx.reply({ embeds: [embed] });
    } else {
      const embed = new EmbedBuilder()
        .setColor(Constants.Colors.invisible)
        .setDescription(`${ctx.getEmoji('x_icon')} ${result.message}`);

      const buttons = new ActionRowBuilder<ButtonBuilder>();

      if (result.canVoteOnTopgg) {
        buttons.addComponents(
          new ButtonBuilder()
            .setStyle(ButtonStyle.Primary)
            .setLabel('Vote on Top.gg')
            .setEmoji('🗳️')
            .setURL(Constants.Links.Vote),
        );
      }

      if (result.requiresPremium) {
        buttons.addComponents(
          new ButtonBuilder()
            .setStyle(ButtonStyle.Link)
            .setLabel('Upgrade to Premium')
            .setEmoji('⭐')
            .setURL(Constants.Links.Donate),
        );
      }

      await ctx.reply({
        embeds: [embed],
        components: buttons.components.length > 0 ? [buttons] : undefined,
      });
    }
  }

  private isValidGifUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // Check for supported GIF domains and formats
      return (
        // Tenor GIFs
        urlObj.hostname.includes('tenor.com') ||
        // Giphy GIFs
        urlObj.hostname.includes('giphy.com') ||
        // Direct GIF URLs
        /\.gif$/i.test(urlObj.pathname)
      );
    } catch {
      return false;
    }
  }
}
